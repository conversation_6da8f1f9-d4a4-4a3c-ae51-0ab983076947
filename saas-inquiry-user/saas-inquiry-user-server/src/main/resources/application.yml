server:
  port: 48080
  shutdown: graceful #开启优雅停机
spring:
  profiles:
    active: test
  application:
    name: inquiry-user-server
  cloud:
    nacos:
      discovery:
        heart-beat:
          enabled: true
      config:
        server-addr: ${spring.cloud.nacos.server-addr}
        import-check:
          enabled: false
          #file-extension: yaml
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  datasource:
    dynamic:
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 20 # 初始连接数
        min-idle: 20 # 最小连接池数量
        max-active: 200 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 30000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 60000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        connectionErrorRetryAttempts: 3     # 创建连接失败时重试3次
        connectionErrorRetryInterval: 10000
      primary: master
  servlet:
    multipart:
      max-file-size: 10MB

  # 定时任务持久化
  quartz:
    job-store-type: jdbc
    wait-for-jobs-to-complete-on-shutdown: true
    overwrite-existing-jobs: false
    jdbc:
      initialize-schema: never

# api方式启动dubbo
dubbo:
  bootstrap:
    enabled: true
  reference:
    - transmitter
  network:
    interface:
      preferred: eth0

  application:
    name: ${spring.application.name}
    qos-port: 22229
    serialize-check-status: WARN
  registry:
    address: nacos://${spring.cloud.nacos.server-addr}
    register-mode: instance
    parameters:
      namespace: ${spring.cloud.nacos.discovery.namespace}
    group: dubbo
  config-center:
    address: nacos://${spring.cloud.nacos.server-addr}
    group: ${spring.cloud.nacos.config.group}
    namespace: ${spring.cloud.nacos.config.namespace}
  protocol:
    name: dubbo
    port: -1 # 自动选择可用端口
  consumer:
    timeout: 10000
    check: false
#    filter: dubboTenantFilter
    #指定服务提供者的namespace （配置文档中有说明）
    provider-namespace: ${spring.cloud.nacos.discovery.namespace}
    group: ${dubbo.registry.group}
  provider:
    timeout: 10000
#    filter: dubboTenantFilter
    group: ${dubbo.registry.group}
# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启SQL日志
  encryptor:
    password: 44y8BEnCE/xqRziDDtVUJA== # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

easy-trans:
  #启用redis缓存 如果不用redis请设置为false
  is-enable-redis: true
  #启用全局翻译(拦截所有responseBody进行自动翻译)，如果对于性能要求很高可关闭此配置在方法上使用注解翻译
  is-enable-global: true
  #启用平铺模式 手动翻译无效
  #  is-enable-tile: true
  #字典缓存放到redis 微服务模式请开启
  #  dict-use-redis: true
  #使用@RpcTrans来标记哪些类可以进行RPC翻译，默认为关闭，多团队协作推荐开启
  #  is-enable-custom-rpc: true
  # ruoyi相关的框架请开启
  is-enable-map-result: true
  # 反向翻译数据库类型 mysql
  #  db-type: mysql
  # Mybatis-plus 为 3.5.3.2版本以上的3.x 版本请设置为true
  mp-new: true

aj:
  captcha:
    #    jigsaw: classpath:images/jigsaw # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    #    pic-click: classpath:images/pic-click # 滑动验证，底图路径，不配置将使用默认图片；以 classpath: 开头，取 resource 目录下路径
    cache-type: redis # 缓存 local/redis...
    cache-number: 10000 # local 缓存的阈值,达到这个值，清除缓存
    timing-clear: 180 # local定时清除过期缓存(单位秒),设置为0代表不执行
    #    type: clickWord # 验证码类型 default两种都实例化。 blockPuzzle 滑块拼图 clickWord 文字点选
    water-mark: 荷叶问诊 # 右下角水印文字(我的水印)，可使用 https://tool.chinaz.com/tools/unicode.aspx 中文转 Unicode，Linux 可能需要转 unicode
    interference-options: 0 # 滑动干扰项(0/1/2)
    req-frequency-limit-enable: false # 接口请求次数一分钟限制是否开启 true|false
    req-get-lock-limit: 5 # 验证失败 5 次，get接口锁定
    req-get-lock-seconds: 10 # 验证失败后，锁定时间间隔
    req-get-minute-limit: 30 # get 接口一分钟内请求数限制
    req-check-minute-limit: 60 # check 接口一分钟内请求数限制
    req-verify-minute-limit: 60 # verify 接口一分钟内请求数限制


springdoc:
  api-docs:
    enabled: true
    path: /system/v3/api-docs
  swagger-ui:
    enabled: true
    path: /system/swagger-ui.html
--- #################### 微信公众号相关配置 ####################
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
  mp:
    # 公众号配置(必填)
    app-id: wx041349c6f39b268b
    secret: 5abee519483bc9f8cb37ce280e814bd0
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
  miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
    appid: wxd1425c818778593e
    secret: 32ab10e980c95f7eeb6d7163d35c2649
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wa # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
justauth:
  enable: true
  type:
    WECHAT_MINI_APP:
      client-id: wxd1425c818778593e
      client-secret: 32ab10e980c95f7eeb6d7163d35c2649
      ignore-check-state: true
      redirect-uri: http://localhost:8080/wx/wxMiniApp/callback
      request-class: com.xingyuv.jushauth.config.AuthDefaultSource


yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao,com.xyy.saas
  web:
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址
    higress-header-params:
      - X-Mse-Consumer
      - XSub
      - XToken
      - XThirdAppId
  xss:
    enable: false
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /isDubboActive # 判断平滑发版服务探活
      - /actuator/** # spring监控
  websocket:
    enable: false # websocket的开关
    path: /infra/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: 荷叶问诊快速开发平台
    description: 提供管理后台、用户 App 的所有功能
    version: ${yudao.info.version}
    url: ${yudao.web.admin-ui.url}
    email: ybm100.com
    license: MIT
    license-url: https://gitee.com/zhijiantianya/ruoyi-vue-pro/blob/master/LICENSE
    http-domain: https://inquiry.test.ybm100.com
  codegen:
    base-package: ${yudao.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
    unit-test-enable: true
  tenant: # 多门店相关配置项
    enable: true
    ignore-urls:
      - /**/system/tenant/default-config # App获取系统默认配置
      - /**/system/tenant/get-list-by-user # 获取当前用户关联的门店列表
      - /**/system/auth/login # 更改登录后选多门店逻辑 此处忽略门店id
      - /**/system/auth/weixin-mini-app-login # 更改登录后选多门店逻辑 此处忽略门店id
      - /**/system/auth/sms-login # 更改登录后选多门店逻辑 此处忽略门店id
      - /**/system/auth/login-confirm-with-tenant-id # 多门店二次选择门店登录接口放行
      - /**/system/auth/OA-login # OA登录放行
      - /**/system/auth/send-sms-code # 发送验证码放行
      - /**/system/auth/api-login # 三方授权登陆放行
      - /**/system/tenant/get-id-by-name # 基于名字获取门店，不许带门店编号
      - /**/system/tenant/get-by-website # 基于域名获取门店，不许带门店编号
      - /**/system/captcha/get # 获取图片验证码，和门店无关
      - /**/system/captcha/check # 校验图片验证码，和门店无关
      - /admin-api/infra/file/*/get/** # 获取图片，和门店无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上门店编号
      - /admin-api/pay/notify/** # 支付回调通知，不携带门店编号
      - /jmreport/* # 积木报表，无法携带门店编号
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，无法携带门店编号
      - /**/product/search/** # 商品搜搜服务忽略
      - /**/product/rational/** # 合理用药审核接口
      - /actuator/** # spring监控
    ignore-tables:
      - system_tenant
      - system_tenant_certificate
      - saas_app_version
      - saas_app_version_detail
      - saas_tenant_package_relation
      - system_users # 更改登录后选多门店逻辑 此表忽略门店id
      - saas_oa_white_list
      - system_role_menu
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - system_social_user
      - system_social_user_bind
      - member_user
      - saas_transmission_organ
      - saas_transmission_organ_dict
      - saas_transmission_organ_dict_match
      - saas_tenant_transmission_service_pack_relation
      - saas_transmission_service_pack
      - saas_transmission_config_item
      - saas_transmission_config_package
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - jimu_dict
      - jimu_dict_item
      - jimu_report
      - jimu_report_data_source
      - jimu_report_db
      - jimu_report_db_field
      - jimu_report_db_param
      - jimu_report_link
      - jimu_report_map
      - jimu_report_share
      - rep_demo_dxtj
      - rep_demo_employee
      - rep_demo_gongsi
      - rep_demo_jianpiao
      - tmp_report_data_1
      - tmp_report_data_income
      - saas_product_stdlib
      - saas_product_info
      - saas_product_qualification_info
      - saas_product_use_info
      - saas_product_transfer_record
      - saas_product_quality_change_record
      - saas_product_quality_change_detail
      - saas_product_price_adjustment_record
      - saas_product_price_adjustment_detail
      - saas_bpm_business_relation
      - saas_product_stdlib_sync
      - saas_product_category
      - saas_catalog
      - saas_regulatory_catalog_detail
    ignore-caches:
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template
    ignore-tenant-ids: # 忽略的门店id
      - -1
      - -2
  sms-code: # 短信验证码相关的配置项
    expire-times: 5m
    send-frequency: 1m
    send-maximum-quantity-per-day: 50 # 每日发送最大数量不能为空
    begin-code: 000000 # 这里配置 1111 的原因是，测试方便。
    end-code: 999999 # 这里配置 1111 的原因是，测试方便。
  trade:
    order:
      app-id: 1 # 商户编号
      pay-expire-time: 2h # 支付的过期时间
      receive-expire-time: 14d # 收货的过期时间
      comment-expire-time: 7d # 评论的过期时间
    express:
      client: kd_niao
      kd-niao:
        api-key: cb022f1e-48f1-4c4a-a723-9001ac9676b8
        business-id: 1809751
      kd100:
        key: pLXUGAwK5305
        customer: E77DF18BE109F454A5CD319E44BF5177

# token配置
jose4j:
  token:
    # 配置尽量不要调整
    # 令牌密钥
    keypair: |
      {
        "p": "63GJh-NOKm0lyueKJWlSfcWx8ugXEcFVhNt33bmwggMN5kOop4AW3R31-uSe8zdTYQQ4BFpegXPQERI-UAgj4YUwg4n7bIin7sAm0HosikqYPc3qjGkvLSROHl29wv7qQxltaoyGJuwdHa1m7MWagY8FSWl4QwM3qkor4wh-AHc",
        "kty": "RSA",
        "q": "rHYNxuE6ml7tFDEzE3eFa5JI39ojlbjt13WRhaqLHDCCYfv8xqeknupX3m97cIYyF6VD9M-kRieSUQGE2X7QoOYdmACxNN759x-0f-j44bt3V13oKwNN6OvS9guID6-iWe4_z0_iowsM3wOF8KLy6kTPectrtv145vGV1YLyYts",
        "d": "nXLX2iLBTggEsxBgmKXUCNrDsZ0ToqkdIAPPWhI2ZO_c4qPpsVx_gQdqD0do62S4hGgJpw99h5ilicqXjlJ87mzk57FRQv0R5BGF2bmm1mmG1guM76fyQ9jQNDGKAn3z9quCQj77J9_3LVWoOnF6wzVcgFLOttXl9SzDm-Ig1Ghl3r6_f49PifYQR9LWvtpab2itCbm9JlVpJ7EH8kYb4258npLIc5byOqMeMSp5me3wFAIKlCJTDlQFwqBUNECEkslTgDmpypzL6JqjQugBG0716mhM7qLModwdPM9HFQ_t4HIfVjEctJ1Lue9ExSzKM5Ff00jUzwMDwVBNzowXgQ",
        "e": "AQAB",
        "use": "sig",
        "kid": "8986c8b17fc641d6be2522844e4faef9",
        "qi": "NBwbU0f72bHqlqrKAqxVGw6Hw4LZbc6rdWM6hKo8Zw6Xjc1Bqn_mTIdfNl4halKHoI7p4A7agx57qgSxwCsbGZYIfqtxnf425gIHHQ8O1RerkGCk18D9jTpZfbc6MLFI8WXdi7ergTUT1ZymARPhh37X_NiKLE8qatMSrdRBIFE",
        "dp": "r7p_TxheGBZ9NbOZjZDNzCgJSGboCQ2HGfGAxtI4puFGt43aTKKy7fjVxpxq8tdheTDf6ofUr8RUENnP_oYE2edCLXuIWBqrVOzAS6xUoCdK-B5AKOq9FnFZiRqobuk6yjgpTXRzEIv8s1DgWGqohnjMFl4NL98v5f8UQJDvEsM",
        "alg": "PS512",
        "dq": "cYSbeOdT7bzCDOJ9nji0B6SYd1Bcz_aUB5iJmxQw0PNVPy3gqto6T6gtAmsbM2wBmPLdkk0C8nKlqtB3I7qHlQEjRaRfAFAs9O9XnkI5YExHN6jGY1mfdMuVZwMcIkcXdF6QfeM1aOCjLkBhb42ym2WDs8WkNyUM6ebnG01AQrU",
        "n": "npzbaF5MP8DcWDqBW5IeqCobf_Coe6SUX1OsQNsraIpFFAZYOrRC2uFRj57B3k53CFAkUzUoQpMAw99kNM0LtuC4rt4ZY-mrAca-kI6L1kIZLsod810wpwQ7_X0IpDvmxVFxotKIJiKf-UAt5ilCkYDxenTqQN49fl-sK3c8R5D_ECf8SC5qfAJaXcG_78T_Kqj1tirQp4rRymciznDQGqv8PjIc9d1hZ_aOfA33Nm-55b8VlXksycBmcpsVNXBjhKuREJTe3yiY9QmS_ZI8D3E9gNBKNqfhkl4FxsGesWj9LFHud1vEcotTiwDBaH7f7eEt4ZBlYd6aUR3UXnXzzQ"
      }
    # 令牌有效期（默认30分钟）
    expireSeconds: 18000

# 激活 Spring 的 databaseIdProvider
# spring.mybatis.database-id-provider.enabled=true
# 或者直接在 MyBatis 配置中
# mybatis.configuration.database-id= # 让 MyBatis 自动检测或在 profile 中设置 


management:
  endpoints:
    web:
      exposure:
        include: "*"
