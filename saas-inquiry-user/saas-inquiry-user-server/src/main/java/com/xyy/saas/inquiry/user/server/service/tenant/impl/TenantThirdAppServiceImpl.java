package com.xyy.saas.inquiry.user.server.service.tenant.impl;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_THIRD_APP_KEY_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_THIRD_APP_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_ORGAN_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_TRANSMISSION_ORGAN_REPETITION;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppPageReqVO;
import com.xyy.saas.inquiry.user.server.controller.admin.tenant.vo.TenantThirdAppSaveReqVO;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;
import com.xyy.saas.inquiry.user.server.dal.mysql.tenant.TenantThirdAppMapper;
import com.xyy.saas.inquiry.user.server.service.tenant.TenantThirdAppService;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 门店三方应用配置 Service 实现类
 */
@Service
@Validated
@Slf4j
public class TenantThirdAppServiceImpl implements TenantThirdAppService {

    @Resource
    private TenantThirdAppMapper tenantThirdAppMapper;

    @Resource
    private TransmissionOrganApi transmissionOrganApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateThirdApp(TenantThirdAppSaveReqVO reqVO) {
        if (reqVO.getTenantId() == null) {
            reqVO.setTenantId(TenantContextHolder.getRequiredTenantId());
        }

        // 1. 校验ERP对接机构
        TransmissionOrganDTO transmissionOrgan = Optional.ofNullable(transmissionOrganApi.getTransmissionOrgan(reqVO.getTransmissionOrganId()))
                .orElseThrow(() -> exception(TENANT_TRANSMISSION_ORGAN_NOT_EXISTS));
        if (reqVO.getId() == null) {
            TenantThirdAppPageReqVO tenantThirdAppPageReqVO = new TenantThirdAppPageReqVO();
            tenantThirdAppPageReqVO.setTenantId(reqVO.getTenantId());
            tenantThirdAppPageReqVO.setTransmissionOrganId(reqVO.getTransmissionOrganId());
            List<TenantThirdAppDO> existDataList = tenantThirdAppMapper.queryByCondition(tenantThirdAppPageReqVO);
            if (CollUtil.isNotEmpty(existDataList)) {
                throw exception(TENANT_TRANSMISSION_ORGAN_REPETITION);
            }
        }

        // 2. 加密密钥
        // String encryptedSecret = cn.hutool.crypto.digest.DigestUtil.sha256Hex(reqVO.getAppSecret());
        
        // 3. 插入或更新
        TenantThirdAppDO thirdApp = BeanUtils.toBean(reqVO, TenantThirdAppDO.class);
        // thirdApp.setAppSecret(encryptedSecret);
        if (thirdApp.getId() == null) {
            thirdApp.setAppKey(generateAndValidAppKey());
            thirdApp.setAppSecret(RandomStringUtils.randomAlphanumeric(32));
            thirdApp.setAppName(StringUtils.defaultIfBlank(reqVO.getAppName(), transmissionOrgan.getName()));
            tenantThirdAppMapper.insert(thirdApp);
        } else {
            tenantThirdAppMapper.updateById(thirdApp);
        }
        
        return thirdApp.getId();
    }

    @Override
    public void resetThirdApp(Long id) {
        // 校验存在
        validateThirdAppExists(id);
        // 重置
        TenantThirdAppDO upd = new TenantThirdAppDO()
                .setId(id)
                .setAppKey(generateAndValidAppKey())
                .setAppSecret(RandomStringUtils.randomAlphanumeric(32));
        tenantThirdAppMapper.updateById(upd);
    }

    /**
     * 生成有效的 appKey
     * @return
     */
    private String generateAndValidAppKey() {
        while (true) {
            // 生成 appKey
            String appKey = RandomStringUtils.randomAlphanumeric(16);
            // 校验唯一性
            try {
                validateAppKeyUnique(appKey, null);
            } catch (ServiceException se) {
                if (TENANT_THIRD_APP_KEY_DUPLICATE.getCode().equals(se.getCode())) {
                    continue;
                }
                throw se;
            }
            return appKey;
        }
    }

    @Override
    public void deleteThirdApp(Long id) {
        // 校验存在
        validateThirdAppExists(id);
        // 删除
        tenantThirdAppMapper.deleteById(id);
    }

    @Override
    public TenantThirdAppDO getThirdApp(Long id) {
        return tenantThirdAppMapper.selectById(id);
    }

    /**
     * 获得门店三方应用配置
     *
     * @param appKey
     * @return 门店三方应用配置
     */
    @Override
    public TenantThirdAppDO getByAppKey(String appKey) {
        return tenantThirdAppMapper.selectByAppKey(appKey);
    }

    @Override
    public PageResult<TenantThirdAppDO> getThirdAppPage(TenantThirdAppPageReqVO pageReqVO) {
        if (pageReqVO.getTenantId() == null) {
            pageReqVO.setTenantId(TenantContextHolder.getRequiredTenantId());
        }
        return tenantThirdAppMapper.selectPage(pageReqVO);
    }

    @Override
    public void updateThirdAppStatus(Long id, Integer status) {
        // 校验存在
        validateThirdAppExists(id);
        // 更新状态
        tenantThirdAppMapper.updateById(new TenantThirdAppDO().setId(id).setStatus(status));
    }

    private void validateAppKeyUnique(String appKey, Long id) {
        TenantThirdAppDO app = tenantThirdAppMapper.selectByAppKey(appKey);
        if (app == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的参数
        if (id == null || !id.equals(app.getId())) {
            throw exception(TENANT_THIRD_APP_KEY_DUPLICATE);
        }
    }

    private void validateThirdAppExists(Long id) {
        if (tenantThirdAppMapper.selectById(id) == null) {
            throw exception(TENANT_THIRD_APP_NOT_EXISTS);
        }
    }

    @Override
    public List<TenantThirdAppDO> queryByCondition(TenantThirdAppPageReqVO reqVO) {

        if (reqVO == null || (reqVO.getTenantId() == null && CollUtil.isEmpty(reqVO.getTenantIdList()))) {
            return Lists.newArrayList();
        }

        return tenantThirdAppMapper.queryByCondition(reqVO);
    }
}