package com.xyy.saas.inquiry.im.server.convert.user;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserRespVO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.in.TencentImportImUserDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import com.xyy.saas.inquiry.im.server.util.tencent.GenerateImUserSig;
import java.util.Map;
import com.xyy.saas.inquiry.im.server.util.tencent.TrtcUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：InquiryImUserConvert
 * @Author: xucao
 * @Date: 2024/11/27 11:29
 * @Description: IM用户转换类
 */
@Mapper
public interface InquiryImUserConvert {

    InquiryImUserConvert INSTANCE = Mappers.getMapper(InquiryImUserConvert.class);

    /**
     * IM用户DO 转换为 VO ,并签名返回
     * @param user DO
     * @param sdkAppId 应用id
     * @return 返回签名后的IM用户信息
     */
    default InquiryImUserRespVO buildVOByDO(InquiryImUserDO user,Long sdkAppId) {
        InquiryImUserRespVO result = convertDO2VO(user);
        result.setSdkAppId(sdkAppId);
        result.setUserSign(GenerateImUserSig.genUserSig(user.getAccountId()));
        result.setTrtcAppId(TrtcUtil.getSdkAppId());
        result.setTrtcUserSign(TrtcUtil.getSign(user.getUserId().toString()));
        return result;
    }

    InquiryImUserRespVO convertDO2VO(InquiryImUserDO user);

    default InquiryImUserDO buildDOByUserDTO(AdminUserRespDTO user, ClientChannelTypeEnum clientChannelTypeEnum) {
        return InquiryImUserDO.builder()
                .userId(user.getId())
                .accountId(clientChannelTypeEnum.getImAccount(user.getId(), clientChannelTypeEnum))
                .clientType(clientChannelTypeEnum.getCode())
                .nickName(user.getNickname())
                .build();
    }

    default Map<String, Object> buildCreateImUserDO(InquiryImUserDO user) {
        TencentImportImUserDO imUser = TencentImportImUserDO.builder().userID(user.getAccountId()).nick(user.getNickName()).build();
        return JSON.parseObject(JSON.toJSONString(imUser), Map.class);
    }



}
