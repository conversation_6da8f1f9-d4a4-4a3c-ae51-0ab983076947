package com.xyy.saas.inquiry.im.server.service.message;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_MESSAGE_NOT_EXISTS;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.tencent.TencentImMessageReadStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryUserImMappingDto;
import com.xyy.saas.inquiry.im.server.config.TencentImConfig;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageSaveReqVO;
import com.xyy.saas.inquiry.im.server.convert.message.InquiryImMessageConvert;
import com.xyy.saas.inquiry.im.server.dal.dataobject.message.InquiryImMessageDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentImBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import com.xyy.saas.inquiry.im.server.dal.mysql.message.InquiryImMessageMapper;
import com.xyy.saas.inquiry.im.server.dal.mysql.user.InquiryImUserMapper;
import com.xyy.saas.inquiry.im.server.mq.message.TencentImCallBackEvent;
import com.xyy.saas.inquiry.im.server.mq.producer.TencentImCallBackProducer;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentImClient;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/11/28 16:08
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Service
@Slf4j
public class InquiryImMessageServiceImpl implements InquiryImMessageService {

    @Resource
    private InquiryImMessageMapper inquiryImMessageMapper;

    @Resource
    private InquiryImUserMapper inquiryImUserMapper;

    @Resource
    private TencentImClient tencentImClient;

    @Resource
    private TencentImConfig tencentImConfig;

    @Resource
    private TencentImCallBackProducer tencentImCallBackProducer;

    @Resource
    private InquiryApi  inquiryApi;

    @Resource
    private InquiryDoctorApi  inquiryDoctorApi;

    /**
     * 发送常规消息
     *
     * @param messageDto 消息对象
     * @return boolean
     */
    @Override
    public Boolean sendUserMessage(InquiryImMessageDto messageDto) {
        // 常规消息转换
        Map<String, Object> params = InquiryImMessageConvert.INSTANCE.buildCreateImMessageDO(messageDto);
        return sendMessage(params,tencentImConfig.getApi().getSendMessage());
    }

    /**
     * 发送消息
     * @param params 消息内容
     * @param apiPath  接口路径(单发 || 群发)
     * @return
     */
    Boolean sendMessage(Map<String, Object> params , String apiPath) {
        // 调用腾讯云接口发送消息
        log.info("腾讯IM发送消息,入参:{}", params);
        Map<String, Object> result = tencentImClient.callApi(apiPath , params);
        log.info("腾讯IM发送消息,响应:{}", JSON.toJSONString(result));
        TencentImBaseRespDO baseRespDO = BeanUtil.toBean(result, TencentImBaseRespDO.class);
        return baseRespDO.isSuccess();
    }

    /**
     * 发送系统消息
     * @param messageDto
     * @return
     */
    @Override
    public Boolean sendSystemMessage(InquiryImMessageDto messageDto) {
        // 系统消息转换
        Map<String, Object> params = InquiryImMessageConvert.INSTANCE.buildSystemMessageDO(messageDto);
        return sendMessage(params,tencentImConfig.getApi().getSendMessage());
    }

    /**
     * 批量发送系统消息
     * @param messageDto
     * @return
     */
    @Override
    public Boolean batchSendSystemMessage(InquiryImMessageDto messageDto) {
        // 批量系统消息转换
        Map<String, Object> params = InquiryImMessageConvert.INSTANCE.buildSystemBatchMessageDO(messageDto);
        return sendMessage(params,tencentImConfig.getApi().getSendBatchMessage());
    }

    /**
     * 腾讯IM回调接口
     *
     * @param tencentImCallBackReqVO 回调对象
     * @return boolean
     */
    @Override
    public Boolean onCallBackMessage(TencentImCallBackReqVO tencentImCallBackReqVO) {
        // 发送MQ消息
        tencentImCallBackProducer.sendMessage(TencentImCallBackEvent.builder().msg(JSON.toJSONString(tencentImCallBackReqVO)).build());
        return Boolean.TRUE;
    }


    @Override
    public Long createInquiryImMessage(InquiryImMessageSaveReqVO createReqVO) {
        // 插入
        InquiryImMessageDO inquiryImMessage = InquiryImMessageConvert.INSTANCE.convertVO2DOWithUser(createReqVO);
        inquiryImMessageMapper.insert(inquiryImMessage);
        // 返回
        return inquiryImMessage.getId();
    }

    @Override
    public void updateInquiryImMessage(InquiryImMessageSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryImMessageExists(updateReqVO.getId());
        // 更新
        InquiryImMessageDO updateObj = BeanUtils.toBean(updateReqVO, InquiryImMessageDO.class);
        inquiryImMessageMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryImMessage(Long id) {
        // 校验存在
        validateInquiryImMessageExists(id);
        // 删除
        inquiryImMessageMapper.deleteById(id);
    }

    private void validateInquiryImMessageExists(Long id) {
        if (inquiryImMessageMapper.selectById(id) == null) {
            throw exception(INQUIRY_IM_MESSAGE_NOT_EXISTS);
        }
    }

    @Override
    public InquiryImMessageDO getInquiryImMessage(Long id) {
        return inquiryImMessageMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryImMessageRespVO> getInquiryImMessagePage(InquiryImMessagePageReqVO pageReqVO) {
        PageResult<InquiryImMessageDO> page = inquiryImMessageMapper.selectPage(pageReqVO);
        if (page == null || page.getTotal() == 0) {
            return PageResult.empty();
        }
        List<InquiryImMessageRespVO> voList = InquiryImMessageConvert.INSTANCE.converMsgDOLIST2VOLIST(page.getList());
        // 填充用户名称
        messageListFillUser(voList);
        return new PageResult<>(voList, page.getTotal());
    }

    /**
     * IM回调阅读消息
     *
     * @param callBackReqVO 回调对象
     */
    @Override
    public void readMsg(TencentImCallBackReqVO callBackReqVO) {
        // 查询最后一条未读
        InquiryImMessageDO messageDO = inquiryImMessageMapper.selectLastReadMsg(InquiryImMessageDO.builder().msgTo(callBackReqVO.getReportAccount()).msgFrom(callBackReqVO.getPeerAccount()).msgTime(callBackReqVO.getLastReadTime()).build());
        if (ObjectUtils.isEmpty(messageDO)) {
            return;
        }
        // 设置消息阅读位点
        messageDO.setReadOffset(TencentImMessageReadStatusEnum.READ.getCode());
        inquiryImMessageMapper.updateById(messageDO);
    }

    /**
     * 获得腾讯IM用户消息分页
     *
     * @param pageReqVO 查询条件
     * @return IM消息列表
     */
    @Override
    public List<InquiryImMessageRespVO> getInquiryImMessageList(InquiryImMessagePageReqVO pageReqVO) {
        List<InquiryImMessageDO> messageList = inquiryImMessageMapper.selectList(pageReqVO);
        if (CollectionUtils.isEmpty(messageList)) {
            return List.of();
        }
        List<InquiryImMessageRespVO> result = InquiryImMessageConvert.INSTANCE.converMsgDOLIST2VOLIST(messageList);
        // 填充用户名称
        messageListFillUser(result);
        return result;
    }

    /**
     * 根据问诊单号删除聊天记录
     *
     * @param inquiryPref 问诊单号
     */
    @Override
    public void deleteByInquiryPref(String inquiryPref) {
        inquiryImMessageMapper.deleteByInquiryPref(inquiryPref);
    }

    /**
     * 批量插入消息
     *
     * @param messageDtos
     * @return
     */
    @Override
    public Boolean batchInsertMessage(List<InquiryImMessageDto> messageDtos) {
        List<InquiryImMessageDO> messageList = InquiryImMessageConvert.INSTANCE.convertDTOList2DOList(messageDtos);
        return inquiryImMessageMapper.insertBatch(messageList);
    }

    /**
     * 根据问诊单号批量获取最后一条消息
     *
     * @param inquiryPrefs 问诊单号集合
     * @return 问诊单最后一条消息集合
     */
    @Override
    public List<InquiryLastMessageDto> getLastMessageByInquiryPrefs(List<String> inquiryPrefs) {
        List<InquiryLastMessageDto> messageDtos = inquiryImMessageMapper.selectLastMessageByInquiryPrefs(inquiryPrefs);
        if(CollectionUtils.isEmpty(messageDtos)){
            return List.of();
        }
        return messageDtos.stream().map(InquiryImMessageConvert.INSTANCE::convertDTO).toList();
    }


    /**
     * 填充问诊用户名称
     * @param messageList
     */
    private void messageListFillUser(List<InquiryImMessageRespVO> messageList) {
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        // 收集所有用户名
        List<String> userAccountList = new ArrayList<>();
        userAccountList.addAll(messageList.stream().map(InquiryImMessageRespVO::getMsgFrom).toList());
        userAccountList.addAll(messageList.stream().map(InquiryImMessageRespVO::getMsgTo).toList());
        if (CollectionUtils.isEmpty(userAccountList)) {
            return;
        }
        // 获取IM账号涉及的所有IM用户
        List<InquiryImUserDO> userList = inquiryImUserMapper.selectList(InquiryImMessagePageReqVO.builder().accountList(userAccountList.stream().distinct().toList()).build());
        // accountId -> userId
        Map<String, Long> userMap = userList.stream().collect(Collectors.toMap(InquiryImUserDO::getAccountId, InquiryImUserDO::getUserId, (k1, k2) -> k2));

        // 收集所有问诊单号
        List<String> inquiryPrefList = messageList.stream().map(InquiryImMessageRespVO::getInquiryPref).distinct().toList();
        // 查询问诊单信息
        List<InquiryRecordDto> inquiryRecordList = inquiryApi.getInquiryRecordList(InquiryQueryDto.builder().prefs(inquiryPrefList).build());
        if (CollectionUtils.isEmpty(inquiryRecordList)) {
            return;
        }
        // 问诊对应用户信息
        Map<String, InquiryUserImMappingDto> inquiryMap = new HashMap<>();
        // doctorPref -> doctorUserId
        Map<String, Long> doctorDtos = new HashMap<>();
        // 查询医生信息
        List<String> doctorPrefList = inquiryRecordList.stream().map(InquiryRecordDto::getDoctorPref).distinct().toList();
        if (!CollectionUtils.isEmpty(doctorPrefList)) {
            doctorDtos = inquiryDoctorApi.getInquiryDoctorByPrefList(doctorPrefList).stream().collect(Collectors.toMap(InquiryDoctorDto::getPref, InquiryDoctorDto::getUserId, (k1, k2) -> k2));
        }
        Map<String, Long> finalDoctorDtos = doctorDtos;
        // 循环问诊单填充map
        inquiryRecordList.forEach(inquiryRecord -> {
            Long doctorUserId = finalDoctorDtos.get(inquiryRecord.getDoctorPref());
            inquiryMap.put(inquiryRecord.getPref(),
                InquiryUserImMappingDto.builder().inquiryPref(inquiryRecord.getPref()).patientName(inquiryRecord.getPatientName()).patientUserId(Long.parseLong(inquiryRecord.getCreator())).doctorName(inquiryRecord.getDoctorName())
                    .doctorUserId(doctorUserId).build());
        });
        messageList.forEach(message -> {
            InquiryUserImMappingDto mappingDto = inquiryMap.get(message.getInquiryPref());
            if(ObjectUtil.isEmpty(mappingDto)){
                return;
            }
            // 填充用户名称
            message.setFromUserName(getUserNameByAccountId(message.getMsgFrom(), userMap, mappingDto));
            message.setToUserName(getUserNameByAccountId(message.getMsgTo(), userMap, mappingDto));
        });

    }

    /**
     * 通过accountId获取用户名称
     * @param accountId
     * @param userMap
     * @param mappingDto
     * @return
     */
    private String getUserNameByAccountId(String accountId, Map<String, Long> userMap, InquiryUserImMappingDto mappingDto) {
        // 先获取userId
        Long userId = userMap.get(accountId);
        if(ObjectUtil.equals(mappingDto.getDoctorUserId(), userId)){
            return mappingDto.getDoctorName();
        }
        if(ObjectUtil.equals(mappingDto.getPatientUserId(), userId)){
            return mappingDto.getPatientName();
        }
        return "";
    }

    /**
     * 根据pref查询聊天记录
     *
     * @param inquiryPref 查询条件
     * @return
     */
    @Override
    public List<InquiryImMessageDO> getByInquiryPref(String inquiryPref) {

        if (StringUtils.isBlank(inquiryPref)) {
            return List.of();
        }

        return inquiryImMessageMapper.selectList(new LambdaQueryWrapperX<InquiryImMessageDO>()
            .eq(InquiryImMessageDO::getInquiryPref, inquiryPref)
            .eq(InquiryImMessageDO::getDeleted, false));
    }
}
