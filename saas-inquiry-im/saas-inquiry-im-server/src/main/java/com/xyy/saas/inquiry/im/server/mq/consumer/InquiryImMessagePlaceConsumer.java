package com.xyy.saas.inquiry.im.server.mq.consumer;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessageRespVO;
import com.xyy.saas.inquiry.im.server.convert.message.InquiryImMessageConvert;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryImMessagePlaceEvent;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.signature.api.immessage.InquiryImPdfApi;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_PDF_GENERATE_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_PDF_UPDATE_ERROR;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 21:55
 * @Description: IM消息归档事件消费者
 **/
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_InquiryImMessagePlaceConsumer",
    topic = InquiryImMessagePlaceEvent.TOPIC)
public class InquiryImMessagePlaceConsumer {

    @Resource
    private InquiryImMessageService inquiryImMessageService;

    @Resource
    private InquiryImPdfApi inquiryImPdfApi;

    @Resource
    private InquiryApi inquiryApi;


    @EventBusListener
    public void inquriyImMessagePlaceConsumer(InquiryImMessagePlaceEvent event) {
        String inquiryPref = event.getMsg();
        if(StringUtils.isBlank(inquiryPref)){
            return;
        }
        // 获取问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(inquiryPref);
        // 1、根据问诊单号查询消息列表
        List<InquiryImMessageRespVO> inquiryImMessageList = inquiryImMessageService.getInquiryImMessageList(InquiryImMessagePageReqVO.builder().inquiryPref(inquiryPref).build());
        if (CollectionUtils.isEmpty(inquiryImMessageList)) {
            return;
        }
        List<InquiryImMessageDto> messageDtos = InquiryImMessageConvert.INSTANCE.converMsgVOLIST2DTOLIST(inquiryImMessageList);
        // 2、调签章服务，生成PDF文件
        CommonResult<String> result = inquiryImPdfApi.genarateImPdf(messageDtos);
        if (result.isError()) {
            throw exception(INQUIRY_IM_PDF_GENERATE_ERROR);
        }
        // 3、调问诊服务，更新问诊单imPdf信息
        boolean updateResult = inquiryApi.updateInquiry(InquiryRecordDto.builder().id(inquiryDto.getId()).imPdf(result.getData()).build());
        if (!updateResult) {
            throw exception(INQUIRY_IM_PDF_UPDATE_ERROR);
        }
        // 4、根据问诊单号在聊天记录表删除当前问诊单聊天记录
        //inquiryImMessageService.deleteByInquiryPref(message.getInquiryPref());
    }
}
