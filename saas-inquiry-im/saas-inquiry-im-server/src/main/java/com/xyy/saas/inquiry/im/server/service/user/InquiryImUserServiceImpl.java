package com.xyy.saas.inquiry.im.server.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.CREATE_TENCENT_IM_USER_ERROR;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.INQUIRY_IM_USER_NOT_EXISTS;
import static com.xyy.saas.inquiry.im.enums.ErrorCodeConstants.USER_NOT_EXISTS;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xyy.saas.inquiry.drugstore.api.user.UserApi;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.im.server.config.TencentImConfig;
import com.xyy.saas.inquiry.im.server.controller.app.message.vo.InquiryImMessagePageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserPageReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.user.vo.InquiryImUserSaveReqVO;
import com.xyy.saas.inquiry.im.server.convert.user.InquiryImUserConvert;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentImBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.user.InquiryImUserDO;
import com.xyy.saas.inquiry.im.server.dal.mysql.user.InquiryImUserMapper;
import com.xyy.saas.inquiry.im.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentImClient;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

/**
 * 腾讯IM用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InquiryImUserServiceImpl implements InquiryImUserService {

    @Resource
    private InquiryImUserMapper inquiryImUserMapper;

    @Resource
    private UserApi userApi;

    @Resource
    private TencentImClient tencentImClient;

    @Autowired
    private TencentImConfig tencentImConfig;


    @Override
    public Long createInquiryImUser(InquiryImUserSaveReqVO createReqVO) {
        // 插入
        InquiryImUserDO inquiryImUser = BeanUtils.toBean(createReqVO, InquiryImUserDO.class);
        inquiryImUserMapper.insert(inquiryImUser);
        // 返回
        return inquiryImUser.getId();
    }

    @Override
    public void updateInquiryImUser(InquiryImUserSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryImUserExists(updateReqVO.getId());
        // 更新
        InquiryImUserDO updateObj = BeanUtils.toBean(updateReqVO, InquiryImUserDO.class);
        inquiryImUserMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryImUser(Long id) {
        // 校验存在
        validateInquiryImUserExists(id);
        // 删除
        inquiryImUserMapper.deleteById(id);
    }

    private void validateInquiryImUserExists(Long id) {
        if (inquiryImUserMapper.selectById(id) == null) {
            throw exception(INQUIRY_IM_USER_NOT_EXISTS);
        }
    }




    /**
     * 根据用户id + 客户端类型 获取IM用户信息 ，如果没有则新增 ，最终返回签名后的数据
     * @param userId 编号
     * @param clientChannelTypeEnum 客户端类型
     * @return IM用户信息
     */
    @Override
    @Lock4j(keys = "'" + RedisKeyConstants.IM_USER_LOCK_KEY + "'.concat(#userId).concat(#clientChannelTypeEnum.code)")
    public InquiryImUserRespVO getInquiryImUser(Long userId , ClientChannelTypeEnum clientChannelTypeEnum) {
        //根据userId + 客户端类型查询IM用户信息
        InquiryImUserDO imUserDO = inquiryImUserMapper.selectOne(Wrappers.<InquiryImUserDO>lambdaQuery().eq(InquiryImUserDO::getUserId, userId).eq(InquiryImUserDO::getClientType, clientChannelTypeEnum.getCode()));
        if(ObjectUtils.isEmpty(imUserDO)){
            //新增并返回
            imUserDO = insertAndRetrunImUser(clientChannelTypeEnum);
        }
        return InquiryImUserConvert.INSTANCE.buildVOByDO(imUserDO, tencentImConfig.getSdkAppId());
    }


    private InquiryImUserDO insertAndRetrunImUser(ClientChannelTypeEnum clientChannelTypeEnum) {
        //根据userid 查询用户信息
        AdminUserRespDTO user = userApi.getUser();
        if(ObjectUtils.isEmpty(user)){
            throw exception(USER_NOT_EXISTS);
        }
        InquiryImUserDO imUserDO = InquiryImUserConvert.INSTANCE.buildDOByUserDTO(user, clientChannelTypeEnum);
        //调用腾讯云接口创建im用户
        Map<String, Object> params = InquiryImUserConvert.INSTANCE.buildCreateImUserDO(imUserDO);
        log.info("调用腾讯云接口创建im用户，参数：{}", JSON.toJSONString(params));
        Map<String,Object> result = tencentImClient.callApi(tencentImConfig.getApi().getCreateUser(),params);
        log.info("调用腾讯云接口创建im用户，响应：{}", JSON.toJSONString(result));
        TencentImBaseRespDO baseRespDO = BeanUtil.toBean(result, TencentImBaseRespDO.class);
        if(!baseRespDO.isSuccess()){
            throw exception(CREATE_TENCENT_IM_USER_ERROR);
        }
        //新增im用户
        inquiryImUserMapper.insert(imUserDO);
        return imUserDO;
    }



    @Override
    public PageResult<InquiryImUserDO> getInquiryImUserPage(InquiryImUserPageReqVO pageReqVO) {
        return inquiryImUserMapper.selectPage(pageReqVO);
    }

    /**
     * 根据用户id和客户端类型查询im账号
     *
     * @param userId                用户id
     * @param clientChannelTypeEnum 客户端类型
     * @return 用户IM账号
     */
    @Override
    public String queryUserImAccountId(Long userId, ClientChannelTypeEnum clientChannelTypeEnum) {
        //根据userId + 客户端类型查询IM用户信息
        InquiryImUserDO imUserDO = inquiryImUserMapper.selectOne(Wrappers.<InquiryImUserDO>lambdaQuery().eq(InquiryImUserDO::getUserId, userId).eq(InquiryImUserDO::getClientType, clientChannelTypeEnum.getCode()));
        if(ObjectUtils.isEmpty(imUserDO)){
            throw exception(INQUIRY_IM_USER_NOT_EXISTS);
        }
        return imUserDO.getAccountId();
    }

    @Override
    public List<String> queryUserImAccountList(List<Long> list, ClientChannelTypeEnum clientChannelTypeEnum) {
       List<InquiryImUserDO> userList = inquiryImUserMapper.selectList(InquiryImMessagePageReqVO.builder().userIdList(list).clientChannelType(clientChannelTypeEnum.getCode()).build());
       if(CollectionUtils.isEmpty(userList)){
           return List.of();
       }
       return userList.stream().map(InquiryImUserDO::getAccountId).distinct().toList();
    }

}