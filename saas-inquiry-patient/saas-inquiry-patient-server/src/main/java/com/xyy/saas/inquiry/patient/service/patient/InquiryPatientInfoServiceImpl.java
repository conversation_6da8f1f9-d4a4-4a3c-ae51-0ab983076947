package com.xyy.saas.inquiry.patient.service.patient;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_INFO_NOT_EXISTS;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_QUERY_STRATEGY_NOT_EXISTS;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.security.core.LoginUser;
import com.google.common.collect.Maps;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientMainSuitVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordDetailConvert;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.patient.InquiryPatientInfoMapper;
import com.xyy.saas.inquiry.patient.service.patient.strategy.PatientQueryStrategy;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import net.bytebuddy.dynamic.scaffold.InstrumentedType.Default;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @Date: 2024/10/25 13:36
 * @Description: 患者服务实现类，完成针对患者的业务服务
 */
@Service
public class InquiryPatientInfoServiceImpl implements InquiryPatientInfoService {

    @Resource
    private InquiryPatientInfoMapper inquiryPatientInfoMapper;

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    /**
     * 患者查询策略
     */
    private static final Map<Integer, PatientQueryStrategy> patientQueryParamhandleMap = Maps.newHashMap();

    @Resource
    public void initHandler(List<PatientQueryStrategy> strategies) {
        strategies.forEach(strategy -> patientQueryParamhandleMap.put(strategy.getQueryScene().getCode(), strategy));
    }

    /**
     * 新增或更新问诊患者信息
     *
     * @param patientInfoDO 患者信息
     * @return 保存结果
     */
    @Override
    public InquiryPatientInfoDO saveOrUpdatePatientInfo(InquiryPatientInfoDO patientInfoDO , InquiryRecordDto inquiryDto) {
        InquiryPatientInfoDO existPatientInfoDO = null;
        // 根据问诊场景  患者手机号查询当前患者是否存在
        if (isMiniProgramPatient(inquiryDto.getClientChannelType())) {
            // 小程序端发起
            String creator = StringUtils.isBlank(patientInfoDO.getCreator()) ? Objects.requireNonNull(getLoginUser()).getId().toString() : patientInfoDO.getCreator();
            existPatientInfoDO = inquiryPatientInfoMapper.selectByNameAndMobile(InquiryPatientInfoPageReqVO.builder().creator(creator).name(patientInfoDO.getName()).mobile(patientInfoDO.getMobile()).build());
        } else {
            // 商家端发起
            existPatientInfoDO = inquiryPatientInfoMapper.selectByNameAndMobile(InquiryPatientInfoPageReqVO.builder().name(patientInfoDO.getName()).mobile(patientInfoDO.getMobile()).tenantId(patientInfoDO.getTenantId()).build());
        }
        return saveOrUpdate(patientInfoDO, existPatientInfoDO,inquiryDto.getClientChannelType());
    }

    /**
     * 新增或更新患者信息
     *
     * @param patientInfoDO      当前患者信息
     * @param existPatientInfoDO 根据当前患者关键数据查的存量患者信息
     * @return
     */
    private InquiryPatientInfoDO saveOrUpdate(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO,Integer clientChannelType) {
        // 患者信息不存在，新增患者信息
        if (existPatientInfoDO == null) {
            inquiryPatientInfoMapper.insert(patientInfoDO);
            return patientInfoDO;
        }
        if (isMiniProgramPatient(clientChannelType)) {
            return handleMiniProgramPatientUpdate(patientInfoDO, existPatientInfoDO);
        }
        // 更新患者信息
        extracted(patientInfoDO, existPatientInfoDO);
        return existPatientInfoDO;
    }

    /**
     * 更新患者信息
     * @param patientInfoDO
     * @param existPatientInfoDO
     */
    private void extracted(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO) {
        BeanUtil.copyProperties(patientInfoDO, existPatientInfoDO, "pref");
        inquiryPatientInfoMapper.updateById(existPatientInfoDO);
    }

    /**
     * 判断当前患者信息是否是小程序端发起的(此代码为AI生成)
     *
     * @param clientChannelType
     * @return
     */
    private boolean isMiniProgramPatient(Integer clientChannelType) {
        return ObjectUtil.equals(ClientChannelTypeEnum.MINI_PROGRAM.getCode(), clientChannelType);
    }

    /**
     * 小程序端发起的更新患者信息(此代码为ai生成)
     *
     * @param patientInfoDO
     * @param existPatientInfoDO
     * @return
     */
    private InquiryPatientInfoDO handleMiniProgramPatientUpdate(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO) {
        if (ObjectUtil.equals(existPatientInfoDO.getTenantId(), patientInfoDO.getTenantId())) {
            extracted(patientInfoDO, existPatientInfoDO);
            return existPatientInfoDO;
        }
        // 为其他门店添加此患者 ，先查当前门店有无此患者
        InquiryPatientInfoDO patient = inquiryPatientInfoMapper.selectByNameAndMobile(InquiryPatientInfoPageReqVO.builder().name(patientInfoDO.getName()).mobile(patientInfoDO.getMobile()).tenantId(patientInfoDO.getTenantId()).build());
        if(patient != null){
            // 有此患者则更新
            extracted(patientInfoDO, patient);
            return patient;
        }
        // 无则新增
        patientInfoDO.setCreator(TenantConstant.DEFAULT_USER);
        inquiryPatientInfoMapper.insert(patientInfoDO);
        return patientInfoDO;
    }

    @Override
    public InquiryPatientInfoRespVO getPatientInfoByPref(String patientPref) {
        InquiryPatientInfoDO patientInfoDO = inquiryPatientInfoMapper.selectOne(InquiryPatientInfoDO::getPref, patientPref);
        if (patientInfoDO == null) {
            throw exception(INQUIRY_PATIENT_INFO_NOT_EXISTS);
        }
        // 每次获取患者详情,实时根据身份证号计算年龄
        Optional.ofNullable(BusinessUtil.getAgeByIdCard(patientInfoDO.getIdCard())).ifPresent(patientInfoDO::setAge);
        return PatientInfoConvert.INSTANCE.convertVO(patientInfoDO);
    }

    /**
     * 获得患者信息分页
     *
     * @param pageReqVO 分页查询
     * @return 患者信息分页
     */
    @Override
    public PageResult<InquiryPatientInfoRespVO> getInquiryPatientInfoPage(InquiryPatientInfoQueryReqVO pageReqVO) {
        if (ObjectUtil.isEmpty(pageReqVO.getQueryScene())) {
            pageReqVO.setQueryScene(PatientQuerySenceEnum.TENANT_QUERY.getCode());
        }
        PatientQueryStrategy patientQueryStrategy = patientQueryParamhandleMap.get(pageReqVO.getQueryScene());
        if (ObjectUtil.isEmpty(patientQueryStrategy)) {
            throw exception(INQUIRY_PATIENT_QUERY_STRATEGY_NOT_EXISTS);
        }
        return patientQueryStrategy.query(pageReqVO);
    }

    @Override
    public PageResult<PatientMainSuitVO> getInquiryPatientMainSuitPage(InquiryPatientInfoPageReqVO pageReqVO) {
        // 查询问诊详情
        PageResult<InquiryRecordDetailDO> pageResult = inquiryRecordDetailMapper.selectPage(InquiryRecordDetailConvert.INSTANCE.convertPatientRecord(pageReqVO));
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }
        // 组装问诊记录
        Map<String, InquiryRecordDO> inquiryRecordDOMap = inquiryRecordMapper.selectListByCondition(
                InquiryRecordPageReqVO.builder().tenantId(pageReqVO.getTenantId()).prefs(CollectionUtils.convertList(pageResult.getList(), InquiryRecordDetailDO::getInquiryPref)).build()).stream()
            .collect(Collectors.toMap(InquiryRecordDO::getPref, Function.identity(), (a, b) -> b));
        List<PatientMainSuitVO> mainSuitVOS = pageResult.getList().stream()
            .map(i -> InquiryRecordDetailConvert.INSTANCE.convertPatientMainSuit(i).setInquiryWay(Optional.ofNullable(inquiryRecordDOMap.get(i.getInquiryPref())).map(InquiryRecordDO::getInquiryWayType).orElse(null))).toList();
        return new PageResult<>(mainSuitVOS, pageResult.getTotal());
    }

    /**
     * 远程审核问诊患者信息新增或保存
     *
     * @param patientInfoDO
     * @return
     */
    @Override
    public InquiryPatientInfoDO saveOrUpdateRemoteAuditInquiryPatent(InquiryPatientInfoDO patientInfoDO) {
        // 根据患者编码查询当前患者
        InquiryPatientInfoDO existPatientInfoDO = inquiryPatientInfoMapper.selectOne(InquiryPatientInfoDO::getPref, patientInfoDO.getPref());
        return saveOrUpdate(patientInfoDO, existPatientInfoDO,ClientChannelTypeEnum.APP.getCode());
    }
}
