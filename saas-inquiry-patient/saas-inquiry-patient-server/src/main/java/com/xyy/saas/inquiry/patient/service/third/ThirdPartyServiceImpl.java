package com.xyy.saas.inquiry.patient.service.third;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ThirdPartPrescriptionStatusEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO.ThirdPartyMedicineInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO.ThirdPartyMedicineUsageRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO.ThirdPartyPreInquiryDetailReqDto;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquirySearchReqVO;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyPreInquiryConvert;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyPreInquiryDetailConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyDrugMatchFailRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDetailDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyDrugMatchFailRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryMapper;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对接三方服务
 */
@Slf4j
@Service
public class ThirdPartyServiceImpl implements ThirdPartyService {

    @Resource
    private ThirdPartyPreInquiryMapper thirdPartyPreInquiryMapper;

    @Resource
    private ThirdPartyPreInquiryDetailMapper thirdPartyPreInquiryDetailMapper;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @Resource
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @Resource
    private InquiryHospitalApi inquiryHospitalApi;

    @Resource
    private InquiryPrescriptionDetailApi inquiryPrescriptionDetailApi;

    @Resource
    private ProductSearchApi productSearchApi;

    @Resource
    private ThirdPartyDrugMatchFailRecordMapper thirdPartyDrugMatchFailRecordMapper;

    @Resource
    private TenantThirdAppApi tenantThirdAppApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private TransmissionOrganApi transmissionOrganApi;

    /**
     * 三方预问诊
     *
     * @param thirdPartyPreInquiryReqVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<ThirdPartyPreInquiryRespVO> preInquiry(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo) {

        // 校验预问诊请求参数
        CommonResult<ThirdPartyPreInquiryRespVO> checkPreInquiryParamResult = this.checkPreInquiryParam(thirdPartyPreInquiryReqVo);

        if (checkPreInquiryParamResult != null) {
            return checkPreInquiryParamResult;
        }
        // 获取租户信息
        TenantDto tenantDto = tenantApi.getTenant();
        // 转换DO模型
        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = ThirdPartyPreInquiryConvert.INSTANCE.convertVO2DO(thirdPartyPreInquiryReqVo, tenantDto);
        // 插入预问诊记录
        thirdPartyPreInquiryMapper.insert(thirdPartyPreInquiryDO);
        // 转换预问诊明细
        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = ThirdPartyPreInquiryDetailConvert.INSTANCE.convertDetailDTOs2DOs(thirdPartyPreInquiryDO, thirdPartyPreInquiryReqVo.getDrugList(), tenantDto);
        // 插入预问诊明细
        thirdPartyPreInquiryDetailMapper.insertBatch(thirdPartyPreInquiryDetailDOList);

        return CommonResult.success(ThirdPartyPreInquiryRespVO.builder().preInquiryId(thirdPartyPreInquiryDO.getId()).preInquiryPref(thirdPartyPreInquiryDO.getPref()).build());
    }

    /**
     * 校验预问诊请求参数
     *
     * @param thirdPartyPreInquiryReqVo
     * @return
     */
    private CommonResult<ThirdPartyPreInquiryRespVO> checkPreInquiryParam(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo) {

        if(ObjectUtil.equals(BizChannelTypeEnum.MINI_PROGRAM.getCode(), thirdPartyPreInquiryReqVo.getBizChannelType())){
            return null;
        }

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryReqVo.getDrugList())) {
            return CommonResult.error("药品详情不能为空");
        }

        if (!Arrays.stream(MedicineTypeEnum.ARRAYS).boxed().toList().contains(thirdPartyPreInquiryReqVo.getMedicineType())) {
            return CommonResult.error("药品类型不能为空");
        }

        if (Objects.equals(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), thirdPartyPreInquiryReqVo.getMedicineType())) {

            if (thirdPartyPreInquiryReqVo.getDrugList().size() > 5) {
                return CommonResult.error("西药处方药品不能超过5种");
            }

        } else if (Objects.equals(MedicineTypeEnum.CHINESE_MEDICINE.getCode(), thirdPartyPreInquiryReqVo.getMedicineType())) {

            if (thirdPartyPreInquiryReqVo.getDrugList().size() > 33) {
                return CommonResult.error("中药处方药品不能超过33种");
            }

            // 全部的药品名称
            Set<String> allDrugNameSet = new HashSet<>();
            // 重复的药品名称
            Set<String> repeatDrugNameSet = new HashSet<>();

            for (ThirdPartyPreInquiryDetailReqDto thirdPartyPreInquiryDetailReqDto : thirdPartyPreInquiryReqVo.getDrugList()) {
                // 如果添加失败，说明该药品名称已经存在，即为重复药品
                if (!allDrugNameSet.add(thirdPartyPreInquiryDetailReqDto.getDrugName())) {
                    repeatDrugNameSet.add(thirdPartyPreInquiryDetailReqDto.getDrugName());
                }
            }

            if (CollectionUtils.isNotEmpty(repeatDrugNameSet)) {
                return CommonResult.error("中药名称不能重复:" + String.join(",", repeatDrugNameSet));
            }
        }
        return null;
    }

    /**
     * 三方获取处方详情
     *
     * @param preInquiryId
     * @return
     */
    @Override
    public CommonResult<ThirdPartyGetPrescriptionRespVO> getPrescription(Long preInquiryId) {

        // 查询三方预问诊详情
        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectById(preInquiryId);

        if (StringUtils.isBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
            return CommonResult.success(ThirdPartyGetPrescriptionRespVO.builder().status(ThirdPartPrescriptionStatusEnum.PHYSICIAN_PREPARING_PRESCRIPTION.getCode()).build());
        }

        // 查询问诊记录
        InquiryRecordDO inquiryDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, thirdPartyPreInquiryDO.getInquiryPref());

        if (inquiryDO == null) {
            return CommonResult.success(ThirdPartyGetPrescriptionRespVO.builder().status(ThirdPartPrescriptionStatusEnum.PHYSICIAN_PRESCRIPTION_FAILED.getCode()).failMsg("荷叶问诊医生开方异常,请重新问诊").build());
        }

        // 查询处方
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().inquiryPref(thirdPartyPreInquiryDO.getInquiryPref()).build());

        if (prescription == null || StringUtils.isBlank(prescription.getPref())) {

            return CommonResult.success(ThirdPartyGetPrescriptionRespVO.builder().status(ThirdPartPrescriptionStatusEnum.transitionInquiryStatusEnum(inquiryDO.getInquiryStatus()).getCode()).failMsg(inquiryDO.getCancelReason()).build());
        }

        ThirdPartyGetPrescriptionRespVO thirdPartyGetPrescriptionRespVO = ThirdPartyGetPrescriptionRespVO.builder().prescriptionUrl(prescription.getPrescriptionImgUrl())
            .status(ThirdPartPrescriptionStatusEnum.transitionPrescriptionStatusEnum(prescription.getStatus()).getCode()).failMsg(prescription.getInvalidReason()).physicianName(prescription.getDoctorName())
            .diagnosis(StringUtils.join(prescription.getDiagnosisName(), ",")).build();

        if (StringUtils.isNotBlank(prescription.getHospitalPref())) {
            InquiryHospitalRespDto inquiryHospitalRespDto = inquiryHospitalApi.getInquiryHospitalByPref(prescription.getHospitalPref());
            if (inquiryHospitalRespDto != null) {
                thirdPartyGetPrescriptionRespVO.setHospital(inquiryHospitalRespDto.getName());
            }
        }

        ThirdPartyMedicineUsageRespVO thirdPartyMedicineUsageRespVO = ThirdPartyMedicineUsageRespVO.builder().medicineType(prescription.getMedicineType()).build();

        // 查询处方药品详情
        List<InquiryPrescriptionDetailRespDTO> prescriptionDetailList = inquiryPrescriptionDetailApi.getPrescriptionDetail(prescription.getPref());

        if (CollectionUtils.isNotEmpty(prescriptionDetailList)) {
            List<ThirdPartyMedicineInfoRespVO> thirdPartyMedicineInfoRespVOList = prescriptionDetailList.stream().map(
                item -> ThirdPartyMedicineInfoRespVO.builder().drugName(item.getCommonName()).attributeSpecification(item.getAttributeSpecification()).manufacturer(item.getManufacturer()).packageUnit(item.getPackageUnit())
                    .productUnit(item.getSingleUnit()).quantity(item.getQuantity()).useFrequency(item.getUseFrequency()).directions(item.getDirections()).singleDose(item.getSingleDose()).build()).toList();
            thirdPartyMedicineUsageRespVO.setDrugList(thirdPartyMedicineInfoRespVOList);
        }

        thirdPartyGetPrescriptionRespVO.setMedicineUsage(thirdPartyMedicineUsageRespVO);

        return CommonResult.success(thirdPartyGetPrescriptionRespVO);
    }

    /**
     * 获取三方问诊列表
     *
     * @param thirdPartyPreInquirySearchReqVO
     * @return
     */
    @Override
    public CommonResult<PageResult<ThirdPartyPreInquiryInfoRespVO>> getPreInquiryPageList(ThirdPartyPreInquirySearchReqVO thirdPartyPreInquirySearchReqVO) {

        thirdPartyPreInquirySearchReqVO.setTenantId(thirdPartyPreInquirySearchReqVO.getTenantId() == null ? TenantContextHolder.getTenantId() : thirdPartyPreInquirySearchReqVO.getTenantId());

        log.info("ThirdPartyService#getPreInquiryPageList:{}", JSONObject.toJSONString(thirdPartyPreInquirySearchReqVO));

        // 查询预订单列表
        PageResult<ThirdPartyPreInquiryDO> preInquiryPage = thirdPartyPreInquiryMapper.selectPage(thirdPartyPreInquirySearchReqVO);

        if (CollectionUtils.isEmpty(preInquiryPage.getList())) {
            return CommonResult.success(new PageResult<>());
        }

        List<Long> thirdPartyPreInquiryIdList = preInquiryPage.getList().stream().map(ThirdPartyPreInquiryDO::getId).toList();

        // 查询预订单详情列表
        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = thirdPartyPreInquiryDetailMapper.selectList(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryIdList);
        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return CommonResult.success(new PageResult<>());
        }
        Map<Long, List<ThirdPartyPreInquiryDetailDO>> preInquiryMap = thirdPartyPreInquiryDetailDOList.stream().collect(Collectors.groupingBy(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId));

        // 三方服务商名称map
        Map<Integer, String> transmissionOrganMap = new HashMap<>();
        List<Integer> transmissionOrganIdList = preInquiryPage.getList().stream().map(ThirdPartyPreInquiryDO::getTransmissionOrganId).distinct().toList();
        if (CollectionUtils.isNotEmpty(transmissionOrganIdList)) {
            List<TransmissionOrganDTO> transmissionOrgans = transmissionOrganApi.getTransmissionOrgans(transmissionOrganIdList);
            if (CollectionUtils.isNotEmpty(transmissionOrgans)) {
                transmissionOrganMap = transmissionOrgans.stream().collect(Collectors.toMap(TransmissionOrganDTO::getId, TransmissionOrganDTO::getName, (v1, v2) -> v2));
            }
        }

        PageResult<ThirdPartyPreInquiryInfoRespVO> preInquiryRespPage = ThirdPartyPreInquiryConvert.INSTANCE.convertPage(preInquiryPage);

        for (ThirdPartyPreInquiryInfoRespVO thirdPartyPreInquiryDO : preInquiryRespPage.getList()) {

            if (transmissionOrganMap.containsKey(thirdPartyPreInquiryDO.getTransmissionOrganId())) {
                thirdPartyPreInquiryDO.setTransmissionOrganName(transmissionOrganMap.get(thirdPartyPreInquiryDO.getTransmissionOrganId()));
            }
            if (preInquiryMap.containsKey(thirdPartyPreInquiryDO.getId())) {
                thirdPartyPreInquiryDO.setDrugList(ThirdPartyPreInquiryDetailConvert.INSTANCE.convertDOList2VOList(preInquiryMap.get(thirdPartyPreInquiryDO.getId())));
            }

        }

        return CommonResult.success(preInquiryRespPage);
    }

    /**
     * 获取三方问诊详情
     *
     * @param thirdPartyPreInquiryId
     * @return
     */
    @Override
    public CommonResult<ThirdPartyPreInquiryInfoRespVO> getPreInquiryById(Long thirdPartyPreInquiryId) {

        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectOne(ThirdPartyPreInquiryDO::getId, thirdPartyPreInquiryId);

        if (thirdPartyPreInquiryDO == null) {
            return CommonResult.error("此预问诊信息已被删除，请重新推送");
        }

        if (StringUtils.isNotBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
            return CommonResult.error("此信息已发起问诊，不允许重复问诊");
        }

        ThirdPartyPreInquiryInfoRespVO thirdPartyPreInquiryInfoRespVO = ThirdPartyPreInquiryConvert.INSTANCE.convertDo2InfoRespVO(thirdPartyPreInquiryDO);

        // 当为小程序渠道时,直接返回,小程序药品详情直接从ext里面取
        if (ObjectUtil.equal(thirdPartyPreInquiryInfoRespVO.getTransmissionOrganId(), BizChannelTypeEnum.MINI_PROGRAM.getCode())) {
            return CommonResult.success(thirdPartyPreInquiryInfoRespVO);
        }

        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = thirdPartyPreInquiryDetailMapper.selectList(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryId);

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return CommonResult.success(thirdPartyPreInquiryInfoRespVO);
        }

        if (Objects.equals(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), thirdPartyPreInquiryDO.getMedicineType())) {

            // 匹配西药
            List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = this.matchWesternMedicine(thirdPartyPreInquiryDetailDOList);
            thirdPartyPreInquiryInfoRespVO.setDrugList(thirdPartyPreInquiryDetailRespVOList);

            // 插入药品匹配失败记录
            List<ThirdPartyDrugMatchFailRecordDO> matchFailList = thirdPartyPreInquiryDetailRespVOList.stream().filter(item -> StringUtils.isNotBlank(item.getMatchFailMsg())).map(
                item -> ThirdPartyDrugMatchFailRecordDO.builder().tenantId(TenantContextHolder.getTenantId()).transmissionOrganId(thirdPartyPreInquiryDO.getTransmissionOrganId()).commonName(item.getCommonName())
                    .attributeSpecification(item.getAttributeSpecification()).barCode(item.getBarCode()).approvalNumber(item.getApprovalNumber()).matchFailMsg(item.getMatchFailMsg()).build()).toList();

            if (CollectionUtils.isNotEmpty(matchFailList)) {
                thirdPartyDrugMatchFailRecordMapper.insertBatch(matchFailList);
            }

        } else if (Objects.equals(MedicineTypeEnum.CHINESE_MEDICINE.getCode(), thirdPartyPreInquiryDO.getMedicineType())) {

            // 匹配中药
            List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = this.matchChineseMedicine(thirdPartyPreInquiryDetailDOList);
            thirdPartyPreInquiryInfoRespVO.setDrugList(thirdPartyPreInquiryDetailRespVOList);

        }

        return CommonResult.success(thirdPartyPreInquiryInfoRespVO);
    }

    /**
     * 匹配中药
     *
     * @param thirdPartyPreInquiryDetailDOList
     * @return
     */
    private List<ThirdPartyPreInquiryDetailRespVO> matchChineseMedicine(List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList) {

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return Lists.newArrayList();
        }

        List<String> commonNameList = thirdPartyPreInquiryDetailDOList.stream().filter(item -> StringUtils.isNotBlank(item.getCommonName())).map(item -> item.getCommonName().replace("中药饮片", "")).filter(StringUtils::isNotBlank)
            .distinct().toList();

        // 匹配中药
        CommonResult<List<InquiryProductDetailDto>> commonResult = productSearchApi.getProductStandardByProductNamesAndType(commonNameList, MedicineTypeEnum.CHINESE_MEDICINE.getCode());

        Map<String, List<InquiryProductDetailDto>> commonNameMap = new HashMap<>();
        if (commonResult.isSuccess() && CollectionUtils.isNotEmpty(commonResult.getData())) {
            commonNameMap = commonResult.getData().stream().collect(Collectors.groupingBy(InquiryProductDetailDto::getCommonName));
        }

        List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = new ArrayList<>();

        for (ThirdPartyPreInquiryDetailDO item : thirdPartyPreInquiryDetailDOList) {
            ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO = ThirdPartyPreInquiryDetailRespVO.builder().commonName(item.getCommonName()).build();

            thirdPartyPreInquiryDetailRespVOList.add(thirdPartyPreInquiryDetailRespVO);

            if (MapUtils.isEmpty(commonNameMap) || !commonNameMap.containsKey(item.getCommonName()) || commonNameMap.get(item.getCommonName()).size() > 1) {
                continue;
            }

            InquiryProductDetailDto inquiryProductDetailDto = commonNameMap.get(item.getCommonName()).getFirst();

            // 匹配规格并构建结果集
            this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDto);

        }

        return thirdPartyPreInquiryDetailRespVOList;
    }

    /**
     * 匹配西药
     *
     * @param thirdPartyPreInquiryDetailDOList
     * @return
     */
    private List<ThirdPartyPreInquiryDetailRespVO> matchWesternMedicine(List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList) {

        // 69码集合
        List<String> barcodeList = new ArrayList<>();
        // 批准文号集合
        List<String> approvalNoList = new ArrayList<>();

        for (ThirdPartyPreInquiryDetailDO thirdPartyPreInquiryDetailDO : thirdPartyPreInquiryDetailDOList) {
            if (StringUtils.isNotBlank(thirdPartyPreInquiryDetailDO.getBarCode())) {
                barcodeList.add(thirdPartyPreInquiryDetailDO.getBarCode());
                continue;
            }
            if (StringUtils.isNotBlank(thirdPartyPreInquiryDetailDO.getApprovalNumber()) && !Lists.newArrayList("*", "-").contains(thirdPartyPreInquiryDetailDO.getApprovalNumber()) && StringUtils.isNotBlank(
                thirdPartyPreInquiryDetailDO.getAttributeSpecification())) {
                approvalNoList.add(thirdPartyPreInquiryDetailDO.getApprovalNumber());
            }
        }

        // 69码map
        Map<String, List<InquiryProductDetailDto>> barcodeMap = new HashMap<>();
        // 批准文号map
        Map<String, List<InquiryProductDetailDto>> approvalNoMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(barcodeList)) {
            log.info("getProductStandardListByBarcodeList#barcodeList:{}", barcodeList);
            CommonResult<List<InquiryProductDetailDto>> productStandardListByBarcodeList = productSearchApi.getProductStandardListByBarcodeList(barcodeList, MedicineTypeEnum.ASIAN_MEDICINE.getCode());
            log.info("getProductStandardListByBarcodeList#productStandardListByBarcodeList:{}", productStandardListByBarcodeList);
            if (productStandardListByBarcodeList.isSuccess() && CollectionUtils.isNotEmpty(productStandardListByBarcodeList.getData())) {
                barcodeMap = productStandardListByBarcodeList.getData().stream().filter(item -> StringUtils.isNotBlank(item.getBarCode())).collect(Collectors.groupingBy(InquiryProductDetailDto::getBarCode));
            }
        }

        if (CollectionUtils.isNotEmpty(approvalNoList)) {
            log.info("getProductStandardListByApprovalNoList#approvalNoList:{}", approvalNoList);
            CommonResult<List<InquiryProductDetailDto>> productStandardListByApprovalNoList = productSearchApi.getProductStandardListByApprovalNoList(approvalNoList, MedicineTypeEnum.ASIAN_MEDICINE.getCode());
            log.info("getProductStandardListByApprovalNoList#productStandardListByApprovalNoList:{}", productStandardListByApprovalNoList);
            if (productStandardListByApprovalNoList.isSuccess() && CollectionUtils.isNotEmpty(productStandardListByApprovalNoList.getData())) {
                approvalNoMap = productStandardListByApprovalNoList.getData().stream().filter(item -> StringUtils.isNotBlank(item.getApprovalNumber())).collect(Collectors.groupingBy(InquiryProductDetailDto::getApprovalNumber));
            }
        }

        List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = new ArrayList<>();

        for (ThirdPartyPreInquiryDetailDO item : thirdPartyPreInquiryDetailDOList) {

            ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO = ThirdPartyPreInquiryDetailConvert.INSTANCE.convertDO2VO(item);
            thirdPartyPreInquiryDetailRespVOList.add(thirdPartyPreInquiryDetailRespVO);

            if (StringUtils.isNotBlank(item.getBarCode())) {

                List<InquiryProductDetailDto> inquiryProductDetailDtoByBarcodeList = barcodeMap.get(item.getBarCode());

                if (CollectionUtils.isEmpty(inquiryProductDetailDtoByBarcodeList)) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品69码未匹配到");
                    continue;
                }

                if (inquiryProductDetailDtoByBarcodeList.size() == 1) {
                    this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDtoByBarcodeList.getFirst());
                    continue;
                }

                // 当根据69码找到了多条数据，则根据规格继续匹配
                // 当三方数据没有规格时，则提示用户未匹配到药品
                if (StringUtils.isBlank(item.getAttributeSpecification())) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("69码对应多个规格未匹配");
                    continue;
                }

                // 匹配规格并构建结果集
                this.matchingSpecificationAndBuildResult(thirdPartyPreInquiryDetailRespVO, item, inquiryProductDetailDtoByBarcodeList, "69码对应多个规格未匹配");

            } else {

                if (StringUtils.isBlank(item.getApprovalNumber())) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品无批准文号");
                    continue;
                }

                if (StringUtils.isBlank(item.getAttributeSpecification())) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品有批准文号但规格未匹配到");
                    continue;
                }

                List<InquiryProductDetailDto> inquiryProductDetailDtoByApprovalNumberList = approvalNoMap.get(item.getApprovalNumber());

                if (CollectionUtils.isEmpty(inquiryProductDetailDtoByApprovalNumberList)) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品批准文号未匹配到");
                    continue;
                }

                // 匹配规格并构建结果集
                this.matchingSpecificationAndBuildResult(thirdPartyPreInquiryDetailRespVO, item, inquiryProductDetailDtoByApprovalNumberList, "药品有批准文号但规格未匹配到");

            }

        }

        return thirdPartyPreInquiryDetailRespVOList;
    }

    /**
     * 匹配规格并构建结果集
     *
     * @param thirdPartyPreInquiryDetailRespVO
     * @param thirdPartyPreInquiryDetailDO
     * @param inquiryProductDetailDtoList
     * @param matchFailMsg
     */
    private void matchingSpecificationAndBuildResult(ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO,
        ThirdPartyPreInquiryDetailDO thirdPartyPreInquiryDetailDO,
        List<InquiryProductDetailDto> inquiryProductDetailDtoList,
        String matchFailMsg) {

        // 是否匹配的上规格
        boolean isMatch = false;

        for (InquiryProductDetailDto inquiryProductDetailDto : inquiryProductDetailDtoList) {

            if (StringUtils.isBlank(inquiryProductDetailDto.getAttributeSpecification())) {
                continue;
            }

            // 对比规格
            isMatch = inquiryProductDetailDto.getAttributeSpecification().equals(thirdPartyPreInquiryDetailDO.getAttributeSpecification());

            if (isMatch) {
                // 填充药品数据
                this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDto);
                break;
            }

        }

        // 当全匹配未匹配上时，则根据规则匹配
        if (!isMatch) {

            // 三方药品规格
            String matchAttributeSpecification = Arrays.stream(thirdPartyPreInquiryDetailDO.getAttributeSpecification().split("\\*")).map(x -> x.replaceAll("[^0-9.]", "")).collect(Collectors.joining("*"));

            for (InquiryProductDetailDto inquiryProductDetailDto : inquiryProductDetailDtoList) {

                if (StringUtils.isBlank(inquiryProductDetailDto.getAttributeSpecification())) {
                    continue;
                }

                // 中台药品规格
                String oneselfAttributeSpecification = Arrays.stream(inquiryProductDetailDto.getAttributeSpecification().split("\\*")).map(x -> x.replaceAll("[^0-9.]", "")).collect(Collectors.joining("*"));

                // 对比规格
                isMatch = matchAttributeSpecification.equals(oneselfAttributeSpecification);

                // 当匹配上规格则直接退出当前循环
                if (isMatch) {
                    // 填充药品数据
                    this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDto);
                    break;
                }

            }
        }

        // 未匹配上规格
        if (!isMatch) {
            thirdPartyPreInquiryDetailRespVO.setMatchFailMsg(matchFailMsg);
        }
    }

    /**
     * 填充药品数据
     *
     * @param thirdPartyPreInquiryDetailRespVO
     * @param inquiryProductDetailDto
     */
    private void buildThirdPartyPreInquiryDetailRespVO(ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO, InquiryProductDetailDto inquiryProductDetailDto) {
        BeanUtils.copyProperties(inquiryProductDetailDto, thirdPartyPreInquiryDetailRespVO);
    }

    /**
     * 根据预问诊id删除预问诊记录
     *
     * @param thirdPartyPreInquiryId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deletePreInquiryById(Long thirdPartyPreInquiryId) {

        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectOne(
            new LambdaQueryWrapperX<ThirdPartyPreInquiryDO>().eqIfPresent(ThirdPartyPreInquiryDO::getTenantId, TenantContextHolder.getTenantId()).eqIfPresent(ThirdPartyPreInquiryDO::getId, thirdPartyPreInquiryId)
                .eq(ThirdPartyPreInquiryDO::getDeleted, false));

        if (thirdPartyPreInquiryDO == null) {
            return CommonResult.error("此信息已被删除，无需重复删除");
        }

        if (StringUtils.isNotBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
            return CommonResult.error("此信息已发起问诊，不允许删除");
        }

        ThirdPartyPreInquiryInfoRespVO thirdPartyPreInquiryInfoRespVO = ThirdPartyPreInquiryConvert.INSTANCE.convertDo2InfoRespVO(thirdPartyPreInquiryDO);

        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = thirdPartyPreInquiryDetailMapper.selectList(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryId);

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return CommonResult.success(true);
        }

        thirdPartyPreInquiryMapper.deleteById(thirdPartyPreInquiryId);
        thirdPartyPreInquiryDetailMapper.delete(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryId);
        return null;
    }

    @Override
    public CommonResult<Boolean> getOpenThirdTag() {

        return CommonResult.success(CollUtil.isNotEmpty(tenantThirdAppApi.getByTenantId(TenantContextHolder.getRequiredTenantId())));
    }
}
