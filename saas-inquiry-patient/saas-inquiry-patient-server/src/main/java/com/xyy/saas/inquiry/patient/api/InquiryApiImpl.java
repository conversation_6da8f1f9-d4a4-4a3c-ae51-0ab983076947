package com.xyy.saas.inquiry.patient.api;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_RECORD_NOT_EXISTS;

import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordGrabbingDoctorDto;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryImService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryRecordDetailService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/04 16:08
 * @Description: 问诊核心接口实现
 */
// @Service
@DubboService
public class InquiryApiImpl implements InquiryApi {

    @Resource
    private InquiryService inquiryService;

    @Resource
    private InquiryRecordDetailService inquiryRecordDetailService;

    @Resource
    private InquiryImService inquiryImService;

    /**
     * 更新问诊单  impdf 地址 以及 聊天记录 {@link com.xyy.saas.inquiry.im.server.mq.consumer.DoctorSubmitPrescriptionConsumer}
     * <p>
     * 更新问诊单  mp4 url {@link com.xyy.saas.inquiry.im.server.mq.consumer.TranscodingTaskEndConsumer}
     *
     * @param inquiryRecordDto 问诊单信息
     * @return true or false
     */
    @Override
    public boolean updateInquiry(InquiryRecordDto inquiryRecordDto) {
        return inquiryService.updateInquiry(inquiryRecordDto);
    }

    @Override
    public boolean updateInquiryEndTime(InquiryRecordDto inquiryRecordDto) {
        return inquiryService.updateInquiryEndTime(inquiryRecordDto);
    }

    /**
     * 医生接诊 - 乐观锁
     *
     * @param inquiryRecordGrabbingDoctorDto 医生接诊dto
     * @return true-成功 false-失败
     */
    @Override
    public boolean doctorGrabbingInquiry(InquiryRecordGrabbingDoctorDto inquiryRecordGrabbingDoctorDto) {
        return inquiryService.doctorGrabbingInquiry(inquiryRecordGrabbingDoctorDto);
    }

    /**
     * 获取问诊单详情
     *
     * @param InquiryPref 问诊单号
     * @return
     */
    @Override
    public InquiryRecordDto getInquiryRecord(String InquiryPref) {
        return inquiryService.getInquiryDtoByPref(InquiryPref);
    }

    @Override
    public InquiryRecordDto getInquiryDtoByPref(String InquiryPref) {
        return inquiryService.getInquiryDtoByInquiryPref(InquiryPref);
    }

    @Override
    public InquiryRecordDetailDto getInquiryRecordDetail(String InquiryPref) {
        final InquiryRecordDetailDO recordDetailDO = inquiryService.getInquiryRecordDetailByPref(InquiryPref);
        if (ObjectUtils.isEmpty(recordDetailDO)) {
            throw exception(INQUIRY_RECORD_NOT_EXISTS);
        }
        return InquiryRecordConvert.INSTANCE.convertDetailDO2DTO(recordDetailDO);
    }

    /**
     * 问诊单列表查询
     *
     * @param inquiryQueryDto 问诊查询dto
     * @return 问诊列表
     */
    @Override
    public List<InquiryRecordDto> getInquiryRecordList(InquiryQueryDto inquiryQueryDto) {
        List<InquiryRecordDO> inquiryRecordList = inquiryService.getInquiryRecordList(InquiryRecordConvert.INSTANCE.convertQueryDTO2QueryVO(inquiryQueryDto));
        if (CollectionUtils.isEmpty(inquiryRecordList)) {
            return List.of();
        }
        // 查询问诊明细信息
        List<InquiryRecordDetailDO> inquiryRecordDetailList = inquiryRecordDetailService.getInquiryRecordDetailList(inquiryQueryDto);
        // 封装问诊单信息
        return InquiryRecordConvert.INSTANCE.convertRespList(inquiryRecordList, inquiryRecordDetailList);
    }

    @Override
    public void batchNotifyDoctorForInquiryChange(List<String> doctorList) {
        inquiryImService.batchNotifyDoctorForInquiryChange(doctorList);
    }

}
