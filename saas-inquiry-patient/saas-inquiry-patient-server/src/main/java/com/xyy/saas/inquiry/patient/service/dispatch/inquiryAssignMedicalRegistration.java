package com.xyy.saas.inquiry.patient.service.dispatch;

import com.xyy.saas.inquiry.hospital.api.dept.InquiryDeptApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.service.registration.MedicalRegistrationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * @Author: cxy
 * @Description: 问诊登记预约挂号单(三方)
 * @see InquiryAssignDept previous
 * @see InquiryAssignType next
 */
@Component
@Slf4j
public class inquiryAssignMedicalRegistration extends InquiryBaseDispatch {

    @Resource
    private InquiryHospitalApi inquiryHospitalApi;

    @Resource
    @Lazy
    private InquiryDeptApi inquiryDeptApi;

    @Resource
    private MedicalRegistrationService medicalRegistrationService;

    @Override
    public void execute(InquiryRecordDto inquiryDto) {
        log.info("问诊单号：{},开始执行问诊登记预约挂号 调度责任链", inquiryDto.getPref());
        //medicalRegistrationService.saveInquiryMedicalRegistration(inquiryDto);
    }

}
