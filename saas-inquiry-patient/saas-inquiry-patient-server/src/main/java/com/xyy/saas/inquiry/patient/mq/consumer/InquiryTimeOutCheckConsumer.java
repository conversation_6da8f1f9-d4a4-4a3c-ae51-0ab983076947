package com.xyy.saas.inquiry.patient.mq.consumer;

import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantChangeCostDto;
import com.xyy.saas.inquiry.drugstore.mq.InquiryReBackCostEvent;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.dal.redis.tenant.TenantRedisDao;
import com.xyy.saas.inquiry.patient.mq.message.InquiryTimeOutCheckEvent;
import com.xyy.saas.inquiry.patient.mq.producer.InquiryReBackProducer;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryImService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: xucao
 * @Date: 2024/12/31 17:34
 * @Description: 问诊超时检查消费者
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_patient_mq_consumer_InquiryTimeOutCheckConsumer",
    topic = InquiryTimeOutCheckEvent.TOPIC)
public class InquiryTimeOutCheckConsumer {

    @Resource
    private InquiryService inquiryService;

    @Resource
    private TenantRedisDao tenantRedisDao;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private InquiryReBackProducer inquiryReBackProducer;

    @Resource
    private InquiryImService inquiryImService;


    @EventBusListener
    @Transactional(rollbackFor = Exception.class)
    public void receiveInquiryTimeOutCheck(InquiryTimeOutCheckEvent inquiryTimeOutCheckEvent) {
        // 获取问诊单号
        String inquiryPref = inquiryTimeOutCheckEvent.getMsg();
        if (StringUtils.isBlank(inquiryPref)) {
            return;
        }
        // 查询当前问诊单信息
        InquiryRecordDto inquiryDto = inquiryService.getInquiryDtoByInquiryPref(inquiryPref);
        if(inquiryDto == null) {
            return;
        }
        // 超时只处理排队中的问题，不处理其他状态
        Integer inquiryStatus = inquiryDto.getInquiryStatus();
        if (inquiryStatus != InquiryStatusEnum.QUEUING.getStatusCode()) {
            return;
        }
        // 将当前问诊单状态设置为超时取消,更新数据库
        if (!inquiryService.updateInquiry(InquiryRecordDto.builder().id(inquiryDto.getId()).inquiryStatus(InquiryStatusEnum.TIMEOUT_CANCELED.getStatusCode()).build())) {
            return;
        }
        // 获取租户信息
        TenantDto tenantDto = tenantApi.getTenant(inquiryDto.getTenantId());
        // 取消问诊后，通知医生端取消问诊
        sendDoctorInquiryTimeOutEvent(inquiryDto);
        // 排队中取消 ，操作redis取消问诊单
        tenantRedisDao.onDrugstoreCancel(inquiryDto, tenantDto);
        // 退回已扣减的额度
        inquiryReBackProducer.sendMessage(InquiryReBackCostEvent.builder().msg(TenantChangeCostDto.builder().bizId(inquiryDto.getPref())
            .recordType(CostRecordTypeEnum.INQUIRY.getCode()).reBackRecordType(CostRecordTypeEnum.INQUIRY_CANAL.getCode()).build()).build());
    }

    /**
     * 问诊超时场景，需要通知已派单医生更新工作台列表
     * @param inquiryDto
     */
    private void sendDoctorInquiryTimeOutEvent(InquiryRecordDto inquiryDto) {
        // 获取当前问诊单的派单医生记录
        List<String> sendList = RedisUtils.lGetAll(RedisKeyConstants.getSendInquiryKey(inquiryDto.getPref())).stream().map(Object::toString).toList();
        inquiryImService.batchNotifyDoctorForInquiryChange(sendList);
    }
}
