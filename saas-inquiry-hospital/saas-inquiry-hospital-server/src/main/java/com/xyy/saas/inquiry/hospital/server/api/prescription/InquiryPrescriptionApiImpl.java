package com.xyy.saas.inquiry.hospital.server.api.prescription;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import jakarta.annotation.Resource;
import java.util.Optional;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:38
 */
// @Service
@DubboService
@Validated
public class InquiryPrescriptionApiImpl implements InquiryPrescriptionApi {

    @Resource
    @Lazy
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private InquiryHospitalService inquiryHospitalService;

    @Override
    public InquiryPrescriptionRespDTO getInquiryPrescription(InquiryPrescriptionQueryDTO queryDTO) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(queryDTO);
        if (prescriptionRespVO == null) {
            return null;
        }
        // 填充医院信息
        Optional.ofNullable(inquiryHospitalService.getInquiryHospitalInfo(prescriptionRespVO.getHospitalPref())).ifPresent(inquiryHospitalDO -> {
            prescriptionRespVO.setHospitalName(inquiryHospitalDO.getName());
            prescriptionRespVO.setInstitutionCode(inquiryHospitalDO.getInstitutionCode());
        });

        return InquiryPrescriptionConvert.INSTANCE.convertVO2DTO(prescriptionRespVO);
    }

    @Override
    public IPage<PatientSimpleDTO> getPrescriptionPatientList(InquiryPrescriptionQueryDTO queryDTO) {
        return inquiryPrescriptionService.getPrescriptionPatientList(queryDTO);
    }

    @Override
    public void updateInquiryPrescription(InquiryPrescriptionUpdateDTO prescriptionUpdateDTO) {
        inquiryPrescriptionService.updateInquiryPrescription(InquiryPrescriptionConvert.INSTANCE.convertDTO2VO(prescriptionUpdateDTO));
    }

    @Override
    public Long getPrescriptionCount(InquiryPrescriptionQueryDTO queryDTO) {
        return inquiryPrescriptionService.getPrescriptionCount(InquiryPrescriptionConvert.INSTANCE.convertQueryVO(queryDTO));
    }


    @Override
    public boolean distributePharmacist(Long id, Long userId) {
        return inquiryPrescriptionService.distributePharmacist(id, userId);
    }

    @Override
    public boolean releasePharmacist(Long id, Long userId) {
        return inquiryPrescriptionService.releasePharmacist(id, userId);
    }

    @Override
    public void postProcessIssuesPrescription(String pref) {
        inquiryPrescriptionService.postProcessIssuesPrescription(pref);
    }
}
