package com.xyy.saas.inquiry.hospital.server.controller.app.diagnosis;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDO;
import com.xyy.saas.inquiry.hospital.server.service.diagnosis.InquiryDiagnosisService;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC - 问诊诊断信息")
@RestController
@RequestMapping(value = {"/admin-api/kernel/hospital/inquiry-diagnosis", "/app-api/kernel/hospital/inquiry-diagnosis"})
@Validated
public class AppInquiryDiagnosisController {

    @Resource
    private InquiryDiagnosisService inquiryDiagnosisService;

    @Resource
    private ProductSearchApi productSearchApi;

    @PostMapping("/recommend-diagnosis")
    @Operation(summary = "药品推荐诊断")
    public CommonResult<List<InquiryDiagnosisRespVO>> recommendDiagnosis(@RequestBody @Valid InquiryDiagnosticsSearchReqDto reqDto) {
        CommonResult<List<InquiryDiagnosticsSearchRespDto>> result = productSearchApi.productDiagnostics(reqDto);
        if (result.isSuccess() && CollUtil.isNotEmpty(result.getData())) {
            // 过滤存在的诊断+类型
            return success(filterExistDiagnosis(result.getData(), reqDto.getProductSearchList().getFirst().getMedicineType()));
        }
        return success(new ArrayList<>());
    }

    private List<InquiryDiagnosisRespVO> filterExistDiagnosis(List<InquiryDiagnosticsSearchRespDto> searchRespDtos, Integer medicineType) {
        List<String> diagnosisCodes = CollectionUtils.convertList(searchRespDtos, InquiryDiagnosticsSearchRespDto::getDiagnosisCode);
        if (CollUtil.isEmpty(diagnosisCodes)) {
            return new ArrayList<>();
        }
        return inquiryDiagnosisService.queryInquiryDiagnosis(InquiryDiagnosisDto.builder()
            .diagnosisCodes(diagnosisCodes)
            .diagnosisType(medicineType)
            .status(CommonStatusEnum.ENABLE.getStatus()).build());
    }

    @GetMapping("/common-diagnosis")
    @Operation(summary = "常用诊断")
    public CommonResult<List<InquiryDiagnosisRespVO>> commonDiagnosis(@Valid InquiryDiagnosisPageReqVO pageReqVO) {
        List<InquiryDiagnosisDO> pageResult = inquiryDiagnosisService.commonDiagnosis(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryDiagnosisRespVO.class));
    }


    @GetMapping("/pages")
    @Operation(summary = "获得问诊诊断信息分页")
    public CommonResult<PageResult<InquiryDiagnosisRespVO>> getInquiryDiagnosisPages(@Valid InquiryDiagnosisPageReqVO pageReqVO) {
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        PageResult<InquiryDiagnosisDO> pageResult = inquiryDiagnosisService.getInquiryDiagnosisPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquiryDiagnosisRespVO.class));
    }

}