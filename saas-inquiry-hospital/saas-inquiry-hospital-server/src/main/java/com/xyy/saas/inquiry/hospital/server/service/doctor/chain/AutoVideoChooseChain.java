package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_AUTO_VIDEO_NOT_EXISTS;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.ding.DingService;
import com.xyy.saas.inquiry.ding.DingService.Markdown;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorVideoDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorVideoService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName：AutoVideoChooseChain
 * @Author: xucao
 * @Date: 2025/3/10 11:52
 * @Description: 视频自动开方医生视频选取
 */
@Component
@Slf4j
public class AutoVideoChooseChain extends DoctorFilterChain{

    @Resource
    private InquiryDoctorVideoService inquiryDoctorVideoService;

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private DingService dingService;

    @Override
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单号：{},视频自动开方医生视频选取,医生列表：{}", inquiryDto.getPref(), JSON.toJSONString(doctorList));
        // 真人开方  或者  非视频问诊 直接返回
        if (inquiryDto.isManualInquiry() || ObjectUtil.notEqual(InquiryWayTypeEnum.VIDEO.getCode(), inquiryDto.getInquiryWayType())) {
            return;
        }
        // 查询医生视频列表
        List<InquiryDoctorVideoDO> doctorVideoList = inquiryDoctorVideoService.selectByDoctorPref(doctorList.getFirst());
        if(CollectionUtils.isEmpty(doctorVideoList)){
            dingService.send(Markdown
                .title("医生未配置录屏视频")
                .add("问诊单号", inquiryDto.getPref())
                .add("接诊医院", inquiryDto.getHospitalName())
                .add("医生编码", doctorList.getFirst())
            );
            throw exception(INQUIRY_DOCTOR_AUTO_VIDEO_NOT_EXISTS, doctorList.getFirst());
        }
        // 随机选取一个医生视频
        InquiryDoctorVideoDO doctorVideo = doctorVideoList.get((int) (Math.random() * doctorVideoList.size()));
        //更新问诊单据
        inquiryApi.updateInquiry(InquiryRecordDto.builder().id(inquiryDto.getId()).doctorVideoPref(doctorVideo.getPref()).build());
    }
}