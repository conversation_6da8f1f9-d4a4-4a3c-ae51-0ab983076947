package com.xyy.saas.inquiry.hospital.server.controller.admin.prescription;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static com.xyy.saas.inquiry.hospital.server.constant.PrescriptionConstant.PRESCRIPTION_PDF_EXPORT_MAX_SIZE;
import static com.xyy.saas.inquiry.hospital.server.constant.PrescriptionConstant.PRESCRIPTION_PDF_PRINT_MAX_SIZE;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelResp1VO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPdfReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPricingVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 处方记录")
@RestController
@RequestMapping("/hospital/inquiry-prescription")
@Validated
public class InquiryPrescriptionController {

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @PostMapping("/create")
    @Operation(summary = "创建处方记录")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:create')")
    public CommonResult<InquiryPrescriptionRespDTO> createInquiryPrescription(@Valid @RequestBody InquiryPrescriptionSaveReqVO createReqVO) {
        return success(inquiryPrescriptionService.createInquiryPrescription(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新处方记录")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:update')")
    public CommonResult<Boolean> updateInquiryPrescription(@Valid @RequestBody InquiryPrescriptionSaveReqVO updateReqVO) {
        inquiryPrescriptionService.updateInquiryPrescription(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除处方记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:delete')")
    public CommonResult<Boolean> deleteInquiryPrescription(@RequestParam("id") Long id) {
        inquiryPrescriptionService.deleteInquiryPrescription(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得处方记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<InquiryPrescriptionRespVO> getInquiryPrescription(@RequestParam("id") Long id) {
        return success(inquiryPrescriptionService.getInquiryPrescription(id));
    }

    @GetMapping("/print")
    @Operation(summary = "获得处方记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<String> printInquiryPrescription(@RequestParam("id") Long id) {
        return success(inquiryPrescriptionService.printInquiryPrescription(id));
    }


    @PostMapping("/pricing")
    @Operation(summary = "处方划价")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:pricing')")
    public CommonResult<Long> pricingInquiryPrescription(@Valid @RequestBody InquiryPrescriptionPricingVO pricingVO) {
        return success(inquiryPrescriptionService.pricingInquiryPrescription(pricingVO));
    }


    @GetMapping("/page")
    @Operation(summary = "获得处方记录分页")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<PageResult<InquiryPrescriptionRespVO>> getInquiryPrescriptionPage(@Valid InquiryPrescriptionPageReqVO pageReqVO) {
        pageReqVO.isWebQuery();
        return success(inquiryPrescriptionService.getInquiryPrescriptionPage(pageReqVO));
    }

    @PostMapping("/export-excel")
    @Operation(summary = "导出处方记录 Excel")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:export')")
    @ApiAccessLog(operateType = EXPORT)
    @Idempotent(timeout = 5) // 参数维度锁住5s
    public void exportInquiryPrescriptionExcel(@Valid @RequestBody InquiryPrescriptionPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.isWebQuery();
        List<InquiryPrescriptionExcelRespVO> list = inquiryPrescriptionService.getInquiryPrescriptionExcelList(pageReqVO);

        if (TenantConstant.isSystemTenant()) {
            ExcelUtils.write(response, "处方记录.xls", "数据", InquiryPrescriptionExcelResp1VO.class,
                InquiryPrescriptionConvert.INSTANCE.convertVO2ExcelVO1(list));
        } else {
            ExcelUtils.write(response, "处方记录.xls", "数据", InquiryPrescriptionExcelRespVO.class, list);
        }
    }

    @PostMapping("/export-pdf")
    @Operation(summary = "导出处方Pdf")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:export')")
    @ApiAccessLog(operateType = EXPORT)
    @Idempotent(timeout = 3)
    public void exportInquiryPrescriptionPdf(@Valid @RequestBody InquiryPrescriptionPdfReqVO exportPdfVO, HttpServletResponse response) {
        exportPdfVO.setMaxSizeKey(PRESCRIPTION_PDF_EXPORT_MAX_SIZE);
        exportPdfVO.setOperate("导出");
        inquiryPrescriptionService.exportInquiryPrescriptionPdf(exportPdfVO, response);
    }


    @PostMapping("/batch-combiner-print")
    @Operation(summary = "批量拼接打印处方")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    @Idempotent(timeout = 3)
    public CommonResult<List<String>> batchCombinerPrintInquiryPrescription(@Valid @RequestBody InquiryPrescriptionPdfReqVO exportPdfVO) {
        exportPdfVO.setMaxSizeKey(PRESCRIPTION_PDF_PRINT_MAX_SIZE);
        exportPdfVO.setOperate("打印");
        return success(inquiryPrescriptionService.batchCombinerPrintInquiryPrescription(exportPdfVO));
    }

    @PostMapping("/batch-print")
    @Operation(summary = "批量打印处方")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:query')")
    public CommonResult<List<String>> batchPrintInquiryPrescription(@Valid @RequestBody InquiryPrescriptionPdfReqVO exportPdfVO) {
        exportPdfVO.setMaxSizeKey(PRESCRIPTION_PDF_PRINT_MAX_SIZE);
        exportPdfVO.setOperate("打印");
        return success(inquiryPrescriptionService.printInquiryPrescriptions(exportPdfVO));
    }

    @PutMapping("/updateByAdmin")
    @Operation(summary = "管理员更新处方记录")
    @PreAuthorize("@ss.hasPermission('hospital:inquiry-prescription:update')")
    public CommonResult<Boolean> updateInquiryPrescriptionByAdmin(@Valid @RequestBody InquiryPrescriptionSaveReqVO updateReqVO) {
        inquiryPrescriptionService.updateInquiryPrescriptionByAdmin(updateReqVO);
        return success(true);
    }

}