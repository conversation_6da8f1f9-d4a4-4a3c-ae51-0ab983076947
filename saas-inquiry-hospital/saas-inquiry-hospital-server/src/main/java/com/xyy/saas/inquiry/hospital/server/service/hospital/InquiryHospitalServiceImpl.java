package com.xyy.saas.inquiry.hospital.server.service.hospital;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSpecificPrescriptionCaDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSpecificPrescriptionTemplateDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSettingVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalConvert;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalSettingConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalSettingDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalSettingMapper;
import com.xyy.saas.inquiry.hospital.server.service.prescription.dto.IssuesPrescriptionConfigDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionParamDto;
import com.xyy.saas.inquiry.signature.api.prescriptiontemplate.PrescriptionTemplateApi;
import com.xyy.saas.inquiry.util.ConditionUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 医院信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Transactional(rollbackFor = Exception.class)
public class InquiryHospitalServiceImpl implements InquiryHospitalService {

    @Resource
    private InquiryHospitalMapper inquiryHospitalMapper;
    @Resource
    private InquiryHospitalDepartmentService inquiryHospitalDepartmentService;
    @Resource
    private InquiryHospitalSettingMapper inquiryHospitalSettingMapper;

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @Resource
    private InquiryHospitalDeptDoctorMapper inquiryHospitalDeptDoctorMapper;

    @Resource
    private PrescriptionTemplateApi prescriptionTemplateApi;

    @Autowired
    private TenantApi tenantApi;

    @Override
    public Long createInquiryHospital(InquiryHospitalSaveReqVO createReqVO) {
        // id有值，insert不会回写id
        createReqVO.setId(null);
        validateName(null, createReqVO.getName());
        // 插入
        InquiryHospitalDO inquiryHospital = InquiryHospitalConvert.INSTANCE.initConvertVO2DO(createReqVO);

        inquiryHospitalMapper.insert(inquiryHospital);
        // 回更id
        createReqVO.setId(inquiryHospital.getId());

        // 更新配置表数据
        insertOrUpdateSetting(createReqVO);
        // 返回
        return inquiryHospital.getId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInquiryHospital(InquiryHospitalSaveReqVO updateReqVO) {
        Long id = updateReqVO.getId();
        // 校验存在
        validateInquiryHospitalExists(id);
        validateName(id, updateReqVO.getName());
        // 更新
        InquiryHospitalDO updateObj = InquiryHospitalConvert.INSTANCE.convertVO2DO(updateReqVO);
        inquiryHospitalMapper.updateById(updateObj);

        // 更新配置表数据
        insertOrUpdateSetting(updateReqVO);
    }

    @Override
    public void deleteInquiryHospital(Long id) {
        // 校验存在
        validateInquiryHospitalExists(id);
        // 删除
        inquiryHospitalMapper.deleteById(id);
        // 删除配置表数据
        inquiryHospitalSettingMapper.delete(InquiryHospitalSettingDO::getHospitalId, id);
    }

    private InquiryHospitalDO validateInquiryHospitalExists(Long id) {
        InquiryHospitalDO inquiryHospitalDO = inquiryHospitalMapper.selectById(id);
        if (inquiryHospitalDO == null) {
            throw exception(INQUIRY_HOSPITAL_NOT_EXISTS);
        }
        return inquiryHospitalDO;
    }

    private void validateName(Long id, String name) {
        if (StringUtils.isBlank(name)) {
            return;
        }
        InquiryHospitalDO departmentDO = inquiryHospitalMapper.selectOne(InquiryHospitalDO::getName, name);
        if (departmentDO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw exception(INQUIRY_HOSPITAL_EXISTS, name);
        }
        if (!Objects.equals(departmentDO.getId(), id)) {
            throw exception(INQUIRY_HOSPITAL_EXISTS, name);
        }
    }

    @Override
    public InquiryHospitalRespDto getInquiryHospital(Long id) {
        return convertDO2Dto(inquiryHospitalMapper.selectById(id));
    }

    /**
     * 获得医院信息
     *
     * @param pref 医院编码
     * @return 医院信息
     */
    @Override
    public InquiryHospitalRespDto getInquiryHospital(String pref) {
        return convertDO2Dto(inquiryHospitalMapper.selectOne(InquiryHospitalDO::getPref, pref));
    }

    @Override
    public InquiryHospitalRespDto getInquiryHospitalInfo(String pref) {
        if (StringUtils.isBlank(pref)) {
            return null;
        }
        return InquiryHospitalConvert.INSTANCE.convertDo2Dto(inquiryHospitalMapper.selectOne(InquiryHospitalDO::getPref, pref));
    }

    @Override
    public PageResult<InquiryHospitalRespDto> getInquiryHospitalPage(InquiryHospitalPageReqVO pageReqVO) {
        PageResult<InquiryHospitalDO> pageResult = inquiryHospitalMapper.selectPage(pageReqVO);
        List<InquiryHospitalRespDto> respVOS = convertDO2DtoList(pageResult.getList());
        return new PageResult<>(respVOS, pageResult.getTotal());
    }

    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitalsBaseInfo(InquiryHospitalReqDto reqDto) {
        return InquiryHospitalConvert.INSTANCE.convertDo2Dtos(inquiryHospitalMapper.getInquiryHospitals(reqDto));
    }

    /**
     * 获取所有互联网医院列表
     *
     * @return 医院信息
     */
    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitalAllList() {
        return convertDO2DtoList(inquiryHospitalMapper.selectList());
    }

    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitals(InquiryHospitalReqDto reqDto) {
        return convertDO2DtoList(inquiryHospitalMapper.getInquiryHospitals(reqDto));
    }

    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitalListByDisable(Integer disable) {
        return convertDO2DtoList(inquiryHospitalMapper.selectList(InquiryHospitalDO::getDisable, disable));
    }

    // region 处理 医院配置信息

    /**
     * 更新医院配置信息
     *
     * @param saveReqVO
     */
    private void insertOrUpdateSetting(InquiryHospitalSaveReqVO saveReqVO) {
        // 更新配置
        List<InquiryHospitalSettingDO> settingDOS = inquiryHospitalSettingMapper.selectByHospitalId(saveReqVO.getId());
        if (CollUtil.isNotEmpty(settingDOS)) {
            inquiryHospitalSettingMapper.deleteByIds(settingDOS.stream().map(InquiryHospitalSettingDO::getId).collect(Collectors.toList()));
        }
        Map<Integer, InquiryHospitalSettingDO> hospitalSettingMap = settingDOS
            .stream().collect(Collectors.toMap(InquiryHospitalSettingDO::getParamType, Function.identity()));

        List<InquiryHospitalSettingDO> inquiryHospitalSettingDOS = InquiryHospitalSettingConvert.INSTANCE.saveReqVO2SettingDOs(saveReqVO, hospitalSettingMap);
        inquiryHospitalSettingMapper.insertOrUpdateBatch(inquiryHospitalSettingDOS);
    }

    /**
     * DO 转换为 DTO  组装配置信息
     *
     * @param hospitalDO
     * @return
     */
    private InquiryHospitalRespDto convertDO2Dto(InquiryHospitalDO hospitalDO) {
        return convertDO2DtoList(Optional.ofNullable(hospitalDO).stream().toList()).stream().findFirst().orElse(null);
    }

    /**
     * DO 转换为 DTO  组装配置信息
     *
     * @param doList
     * @return
     */
    private List<InquiryHospitalRespDto> convertDO2DtoList(List<InquiryHospitalDO> doList) {
        if (CollectionUtils.isEmpty(doList)) {
            return List.of();
        }
        // 获取医院关联的配置信息
        List<InquiryHospitalSettingDO> settingList = inquiryHospitalSettingMapper.selectByHospitalIds(doList.stream().map(InquiryHospitalDO::getId).toList());
        // 组装配置信息
        Map<Long, Map<Integer, InquiryHospitalSettingDO>> hospitalSettingsMap = settingList.stream().collect(
            Collectors.groupingBy(InquiryHospitalSettingDO::getHospitalId,
                Collectors.toMap(InquiryHospitalSettingDO::getParamType, Function.identity())));

        List<InquiryHospitalRespDto> result = doList.stream().map(dto -> {
            Map<Integer, InquiryHospitalSettingDO> settingDOMap = hospitalSettingsMap.getOrDefault(dto.getId(), Collections.emptyMap());
            return InquiryHospitalSettingConvert.INSTANCE.convertDO2DtoWithSetting(dto, settingDOMap);
        }).toList();

        // 获取科室pref
        List<String> deptPrefList = new ArrayList<>();
        result.stream().map(InquiryHospitalRespDto::getSetting).forEach(setting -> {
            Optional.ofNullable(setting.getDefaultInquiryWesternMedicineDept()).ifPresent(x -> x.forEach(y -> deptPrefList.add(y.getDeptPref())));
            Optional.ofNullable(setting.getDefaultInquiryChineseMedicineDept()).ifPresent(x -> x.forEach(y -> deptPrefList.add(y.getDeptPref())));
        });
        // 组装科室信息
        Map<String, InquiryHospitalDepartmentDO> defaultDeptMap = inquiryHospitalDepartmentService.getDeptPrefMap(deptPrefList);
        result.forEach(dto -> {
            InquiryHospitalSettingDto setting = dto.getSetting();
            Optional.ofNullable(setting.getDefaultInquiryWesternMedicineDept())
                .ifPresent(x -> x.forEach(y -> y.setDeptName(Optional.ofNullable(defaultDeptMap.get(y.getDeptPref())).map(InquiryHospitalDepartmentDO::getDeptName).orElse(null))));
            Optional.ofNullable(setting.getDefaultInquiryChineseMedicineDept())
                .ifPresent(x -> x.forEach(y -> y.setDeptName(Optional.ofNullable(defaultDeptMap.get(y.getDeptPref())).map(InquiryHospitalDepartmentDO::getDeptName).orElse(null))));
        });

        return result;
    }

    /**
     * DTO 转换为 VO  组装配置信息以及关联名称
     *
     * @param dto
     * @return
     */
    @Override
    public InquiryHospitalRespVO convertDto2VO(InquiryHospitalRespDto dto) {
        return convertDto2VOList(Optional.ofNullable(dto).stream().toList()).stream().findFirst().orElse(null);
    }

    /**
     * DTO 转换为 VO  组装配置信息以及关联名称
     *
     * @param dtoList
     * @return
     */
    @Override
    public List<InquiryHospitalRespVO> convertDto2VOList(List<InquiryHospitalRespDto> dtoList) {
        // 默认处方笺模板信息
        Set<Long> preTempIdList = new HashSet<>();
        // 默认科室信息
        Set<String> deptPrefList = new HashSet<>();

        dtoList.forEach(dto -> {
            InquiryHospitalSettingDto settingDto = dto.getSetting();
            Optional.ofNullable(settingDto.getDefaultInquiryWesternMedicineDept()).ifPresent(x -> deptPrefList.addAll(x.stream().map(Dept::getDeptPref).toList()));
            Optional.ofNullable(settingDto.getDefaultInquiryChineseMedicineDept()).ifPresent(x -> deptPrefList.addAll(x.stream().map(Dept::getDeptPref).toList()));
            Optional.ofNullable(settingDto.getDefaultWesternPrescriptionTemplate()).ifPresent(preTempIdList::add);
            Optional.ofNullable(settingDto.getDefaultChinesePrescriptionTemplate()).ifPresent(preTempIdList::add);
            // 特定处方笺
            Optional.ofNullable(settingDto.getExtend())
                .map(i -> Optional.ofNullable(i.getSpecificPrescriptionTemplates())
                    .stream().flatMap(x -> x.stream().map(InquiryHospitalSpecificPrescriptionTemplateDto::getPrescriptionTemplateId))
                    .toList()
                ).ifPresent(preTempIdList::addAll);
        });

        // 查询关联科室 & 处方笺
        Map<String, String> deptPrefMap = inquiryHospitalDepartmentService.getDeptPrefMap(new ArrayList<>(deptPrefList))
            .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getDeptName()));
        Map<Long, String> preTempIdMap = prescriptionTemplateApi.getInquiryPrescriptionTemplate(new ArrayList<>(preTempIdList))
            .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getName()));

        return InquiryHospitalConvert.INSTANCE.convertDtos2VOs(dtoList)
            .stream().peek(vo -> {
                InquiryHospitalSettingVO setting = vo.getSetting();
                Optional.ofNullable(setting.getDefaultInquiryWesternMedicineDept()).map(list -> list.stream().peek(l -> l.setDeptName(deptPrefMap.get(l.getDeptPref()))));
                Optional.ofNullable(setting.getDefaultInquiryChineseMedicineDept()).map(list -> list.stream().peek(l -> l.setDeptName(deptPrefMap.get(l.getDeptPref()))));
                Optional.ofNullable(setting.getDefaultWesternPrescriptionTemplate()).map(preTempIdMap::get).ifPresent(setting::setDefaultWesternPrescriptionTemplateName);
                Optional.ofNullable(setting.getDefaultChinesePrescriptionTemplate()).map(preTempIdMap::get).ifPresent(setting::setDefaultChinesePrescriptionTemplateName);
                // 特定处方笺
                Optional.ofNullable(setting.getExtend())
                    .ifPresent(i -> {
                            Optional.ofNullable(i.getSpecificPrescriptionTemplates())
                                .stream().flatMap(Collection::stream)
                                .forEach(x -> {
                                    Optional.ofNullable(x.getPrescriptionTemplateId()).map(preTempIdMap::get).ifPresent(x::setPrescriptionTemplateName);
                                });
                            Optional.ofNullable(i.getSpecificPrescriptionCas()).ifPresent(cas -> {
                                setting.getExtend().setSpecificPrescriptionCas(cas);
                            });
                        }
                    );
            }).toList();
    }

    // endregion


    @Override
    public IssuesPrescriptionConfigDto getHospitalPrescriptionConfigByInquiry(InquiryRecordDto inquiryRecordDto, InquiryRecordDetailDto inquiryRecordDetailDto) {
        // 2.获取医院及配置
        InquiryHospitalRespDto inquiryHospital = getInquiryHospital(inquiryRecordDto.getHospitalPref());
        if (inquiryHospital == null) {
            return null;
        }
        TenantDto tenantDto = tenantApi.getTenant(inquiryRecordDto.getTenantId());
        ConditionParamDto paramDto = InquiryHospitalConvert.INSTANCE.convertConditionParam(tenantDto, inquiryRecordDto, inquiryRecordDetailDto);

        // 根据条件筛选符合条件的处方笺模板
        List<InquiryHospitalSpecificPrescriptionTemplateDto> specificPrescriptionTemplateDtos = inquiryHospital.getSetting().getExtend().getSpecificPrescriptionTemplates();
        InquiryHospitalSpecificPrescriptionTemplateDto templateDto = ConditionUtil.matchFirst(paramDto, specificPrescriptionTemplateDtos);

        Long templateId = templateDto != null ? templateDto.getPrescriptionTemplateId()
            : Objects.equals(inquiryRecordDto.getMedicineType(), MedicineTypeEnum.ASIAN_MEDICINE.getCode()) ? inquiryHospital.getSetting().getDefaultWesternPrescriptionTemplate()
                : inquiryHospital.getSetting().getDefaultChinesePrescriptionTemplate();
        IssuesPrescriptionConfigDto issuesPrescriptionConfigDto = IssuesPrescriptionConfigDto.builder().prescriptionTemplateId(templateId).build();

        // CA
        List<InquiryHospitalSpecificPrescriptionCaDto> specificPrescriptionCas = inquiryHospital.getSetting().getExtend().getSpecificPrescriptionCas();
        InquiryHospitalSpecificPrescriptionCaDto prescriptionCaDto = ConditionUtil.matchFirst(paramDto, specificPrescriptionCas);
        Optional.ofNullable(prescriptionCaDto).ifPresent(ca -> issuesPrescriptionConfigDto.setSignaturePlatform(ca.getSignaturePlatform()).setSignaturePlatformConfigId(ca.getSignaturePlatformConfigId()));

        return issuesPrescriptionConfigDto;
    }

    @Override
    public Map<Long, Set<InquiryHospitalRespDto>> getPresTempUsedHospitalMap(List<Long> presTempIdList) {
        if (CollUtil.isEmpty(presTempIdList)) {
            return Map.of();
        }
        List<Long> hospitalIds = inquiryHospitalSettingMapper.selectPresTempUsedHospitalIdList(presTempIdList).stream().distinct().toList();
        if (CollUtil.isEmpty(hospitalIds)) {
            return Map.of();
        }
        List<InquiryHospitalRespDto> inquiryHospitalRespDtos = getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalIds(hospitalIds).build());
        if (CollUtil.isEmpty(inquiryHospitalRespDtos)) {
            return Map.of();
        }

        // 使用处方笺id与医院id的映射
        Map<Long, Set<InquiryHospitalRespDto>> presTempIdRelatedHospitalMap = new HashMap<>();
        // 映射医院配置的处方笺模版列表
        inquiryHospitalRespDtos.forEach(dto -> {
            if (dto.getSetting() == null) {
                return;
            }
            // 筛选出使用过的处方笺
            dto.usedPresTempIdList().stream().filter(presTempIdList::contains).forEach(presTempId -> {
                presTempIdRelatedHospitalMap.compute(presTempId, (k, old) -> {
                    old = Optional.ofNullable(old).orElseGet(HashSet::new);
                    old.add(dto);
                    return old;
                });
            });
        });

        return presTempIdRelatedHospitalMap;
    }


    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitalsByDoctorUser(Long userId) {
        InquiryDoctorDO doctor = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getUserId, userId);
        if (doctor == null) {
            return null;
        }
        List<String> hospitalPrefs = inquiryHospitalDeptDoctorMapper.selectDoctorHospitalList(doctor.getPref());
        if (CollUtil.isEmpty(hospitalPrefs)) {
            return null;
        }
        return getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(hospitalPrefs).build());
    }
}