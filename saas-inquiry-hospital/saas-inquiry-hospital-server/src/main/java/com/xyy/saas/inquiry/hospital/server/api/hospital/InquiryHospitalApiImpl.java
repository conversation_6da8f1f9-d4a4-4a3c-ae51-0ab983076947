package com.xyy.saas.inquiry.hospital.server.api.hospital;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.util.StringUtils;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:50
 */
// @Service
@DubboService
public class InquiryHospitalApiImpl implements InquiryHospitalApi {

    @Resource
    @Lazy
    private InquiryHospitalService inquiryHospitalService;

    @Resource
    @Lazy
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    @Lazy
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;


    @Override
    public InquiryHospitalRespDto getInquiryHospital(Long id) {
        return inquiryHospitalService.getInquiryHospital(id);
    }

    /**
     * 根据医院编码获取医院信息
     *
     * @param pref 医院编码
     * @return 医院信息
     */
    @Override
    public InquiryHospitalRespDto getInquiryHospitalByPref(String pref) {
        if (StringUtils.isBlank(pref)) {
            return null;
        }
        return inquiryHospitalService.getInquiryHospital(pref);
    }

    @Override
    public Map<String, InquiryHospitalRespDto> getInquiryHospitalsBaseInfoMap(InquiryHospitalReqDto reqDto) {
        return inquiryHospitalService.getInquiryHospitalsBaseInfo(reqDto).stream().collect(Collectors.toMap(InquiryHospitalRespDto::getPref, Function.identity(), (a, b) -> b));
    }

    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitals(InquiryHospitalReqDto reqDto) {
        return inquiryHospitalService.getInquiryHospitals(reqDto);
    }

    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitalsCaByDoctorUser(Long userId, SignatureAppConfigIdEnum authAppConfigId) {
        List<InquiryHospitalRespDto> hospitals = inquiryHospitalService.getInquiryHospitalsByDoctorUser(userId);
        return getInquiryHospitalRespDtos(authAppConfigId, hospitals);
    }

    @Override
    public List<InquiryHospitalRespDto> getInquiryHospitalsCaByHospital(List<String> hospitalPrefs, SignatureAppConfigIdEnum authAppConfigId) {
        if (CollUtil.isEmpty(hospitalPrefs)) {
            return List.of();
        }
        List<InquiryHospitalRespDto> hospitals = inquiryHospitalService.getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(hospitalPrefs).build());
        return getInquiryHospitalRespDtos(authAppConfigId, hospitals);
    }

    private List<InquiryHospitalRespDto> getInquiryHospitalRespDtos(SignatureAppConfigIdEnum authAppConfigId, List<InquiryHospitalRespDto> hospitals) {
        if (CollUtil.isEmpty(hospitals)) {
            return null;
        }
        return hospitals.stream().filter(h -> h.getSetting().getExtend() != null
            && CollUtil.isNotEmpty(h.getSetting().getExtend().getSpecificPrescriptionCas())
            && h.getSetting().getExtend().getSpecificPrescriptionCas().stream().anyMatch(c -> Objects.equals(c.getSignaturePlatformConfigId(), authAppConfigId.getCode()))).toList();
    }

    @Override
    public Map<String, InquiryHospitalRespDto> getInquiryHospitalsMap(InquiryHospitalReqDto reqDto) {
        return getInquiryHospitals(reqDto).stream().collect(Collectors.toMap(InquiryHospitalRespDto::getPref, Function.identity(), (a, b) -> b));
    }

    @Override
    public Map<Long, Set<InquiryHospitalRespDto>> getPresTempUsedHospitalMap(List<Long> presTempIdList) {
        return inquiryHospitalService.getPresTempUsedHospitalMap(presTempIdList);
    }
}
