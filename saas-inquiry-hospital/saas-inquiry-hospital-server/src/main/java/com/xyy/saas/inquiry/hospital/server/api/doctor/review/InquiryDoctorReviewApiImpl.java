package com.xyy.saas.inquiry.hospital.server.api.doctor.review;

import com.xyy.saas.inquiry.hospital.api.doctor.review.InquiryDoctorReviewApi;
import com.xyy.saas.inquiry.hospital.api.doctor.review.dto.DoctorReviewDto;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorReviewConvert;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorReviewsService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @DateTime: 2025/3/17 16:02
 * @Description: 医生问诊评价相关接口
 **/
// @Service
@DubboService
public class InquiryDoctorReviewApiImpl implements InquiryDoctorReviewApi {

    @Resource
    private DoctorReviewsService doctorReviewsService;

    /**
     * 获取医生问诊评价
     * @param inquiryPref 问诊单pref
     * @return
     */
    @Override
    public DoctorReviewDto getDoctorReview(String inquiryPref) {
        return InquiryDoctorReviewConvert.INSTANCE.convertDO2DTO(doctorReviewsService.getDoctorReviewsByInquiryPref(inquiryPref));
    }
}
