package com.xyy.saas.inquiry.pharmacist.server.service.signature;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.prescription.InquiryPharmacistPrescriptionConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.signature.InquiryPharmacistSignatureConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy.AbstractInquirySignaturePassingStrategy;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
import jakarta.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 药师签章回调service
 */
@Service
@Slf4j
public class InquiryPharmacistSignaturePassingServiceImpl implements InquiryPharmacistSignaturePassingService {

    @Autowired
    private Map<String, AbstractInquirySignaturePassingStrategy> inquirySignaturePassingStrategyMap;

    @Resource
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @Autowired
    protected InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    /**
     * 处方签章回传
     *
     * @param spMessage
     */
    @Override
    public void prescriptionSignaturePassing(SignaturePassingMessage spMessage) {
        // 校验处方状态,完成不再处理 回更签章图片和状态
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(spMessage.getBizId()).build());
        log.info("【SignaturePassing】处方回传药师服务pharmacist-server,pref:{},actorField:{},nextFiled:{},pdfUrl:{}", spMessage.getBizId(), spMessage.getParticipantItem().getActorField(), spMessage.getNextField(),
            spMessage.getPdfUrl());
        if (prescription == null || Objects.equals(PrescriptionStatusEnum.CANCELED.getStatusCode(), prescription.getStatus())
            || Objects.equals(PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode(), prescription.getStatus())) {
            return;
        }

        TenantUtils.execute(prescription.getTenantId(), () -> {
            // 判断审核记录是否审核通过 ,存在则说明不需要处理当前回调
            InquiryPrescriptionAuditDO prescriptionAudit = spMessage.getAuditRecordId() == null ? null : inquiryPrescriptionAuditService.getPrescriptionAudit(spMessage.getAuditRecordId());
            if (prescriptionAudit != null && !PrescriptionAuditStatusEnum.isHanded(prescriptionAudit.getAuditStatus())) {
                return;
            }
            inquiryPrescriptionApi.updateInquiryPrescription(InquiryPharmacistPrescriptionConvert.INSTANCE.convertSignPassingUpdateDto(spMessage, prescription));

            // 更新审核记录
            SignaturePassingHandleDto handleDto = SignaturePassingHandleDto.builder().prescription(InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription)).spMessage(spMessage).build();
            updatePrescriptionAuditRecord(handleDto);
            /**
             * 处理当前节点
             * eg: 回更审核记录、处方回传saas
             */
            Optional.ofNullable(inquirySignaturePassingStrategyMap.get(spMessage.getParticipantItem().getActorField())).ifPresent(s -> s.handleSelf(handleDto));
            /**
             * 根据节点判断下级策略
             * eg: 推送审方池、推送消息、直接自绘下级核对发药签名
             */
            if (spMessage.getNextField() != null) {
                Optional.ofNullable(inquirySignaturePassingStrategyMap.get(spMessage.getNextField().getField())).ifPresent(s -> s.handleNext(handleDto));
            }
        });

    }


    /**
     * 更新签章审核记录
     */
    public void updatePrescriptionAuditRecord(SignaturePassingHandleDto sphDto) {
        if (sphDto.getSpMessage().getAuditRecordId() == null) {
            return;
        }
        InquiryPrescriptionAuditSaveReqVO auditSaveReqVO = InquiryPharmacistSignatureConvert.INSTANCE.convertSaveAuditRecord(sphDto, PrescriptionAuditStatusEnum.APPROVED, SignatureStatusEnum.SIGNED);
        inquiryPrescriptionAuditService.updatePrescriptionAudit(auditSaveReqVO);
    }
}