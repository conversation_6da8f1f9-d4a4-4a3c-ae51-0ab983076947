package com.xyy.saas.inquiry.pharmacist.server.service.pharmacist;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_HAS_ROLE_NO_ALLOW_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_IS_DISABLE;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_CREATE_EXISTS;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_IS_AUDIT;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_NOT_EXISTS;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_ONLINE_LOGOFF_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_STATUS_ERROR;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.permission.RoleApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantUserRelationApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationBindDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.UserFingerPrintApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DrawnSignEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.user.ClockInTypeEnum;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.api.doctor.indentification.InquiryProfessionIdentificationApi;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistUpdateStatusReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.pharmacist.vo.PharmacistUpdateInfoVo;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist.InquiryPharmacistMapper;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PharmacistAuditEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PharmacistAuditMessageDTO;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.PharmacistDelayAuditProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistQueryDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.signature.InquiryUserSignatureInformationApi;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 药师信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryPharmacistServiceImpl implements InquiryPharmacistService {

    @Resource
    private InquiryPharmacistMapper inquiryPharmacistMapper;

    @Resource
    private InquiryTenantPharmacistRelationService inquiryTenantPharmacistRelationService;

    @Autowired
    private AdminUserApi adminUserApi;

    @Autowired
    private PermissionApi permissionApi;

    @Autowired
    private RoleApi roleApi;

    @Autowired
    private TenantUserRelationApi tenantUserRelationApi;

    @Autowired
    private InquiryProfessionIdentificationApi inquiryProfessionIdentificationApi;

    @Resource
    private InquiryUserSignatureInformationApi inquiryUserSignatureInformationApi;

    @Resource
    private PharmacistDelayAuditProducer pharmacistDelayAuditProducer;

    @Resource
    @Lazy
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @Autowired
    private UserFingerPrintApi userFingerPrintApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InquiryPharmacistDO createInquiryPharmacistSystem(InquiryPharmacistSaveReqVO createReqVO) {
        InquiryPharmacistDO pharmacistDO = createInquiryPharmacist(createReqVO, AuditStatusEnum.APPROVED);
        return pharmacistDO;
    }

    /**
     * 创建药师
     *
     * @param createReqVO     创建VO
     * @param auditStatusEnum 审核状态
     * @return 药师信息
     */
    private @Nonnull InquiryPharmacistDO createInquiryPharmacist(InquiryPharmacistSaveReqVO createReqVO, AuditStatusEnum auditStatusEnum) {
        // save user
        AdminUserSaveDTO userSaveDTO = InquiryPharmacistConvert.INSTANCE.convertUser(createReqVO);
        if (Objects.equals(createReqVO.getPharmacistType(), PharmacistTypeEnum.PLATFORM.getCode())) {
            userSaveDTO.setNeedClockIn(CommonStatusEnum.ENABLE.getStatus()); // 超管创建平台药师时需要打卡
        }
        if (TenantConstant.isSystemTenant() && Objects.equals(createReqVO.getPharmacistType(), PharmacistTypeEnum.DRUGSTORE.getCode())) {
            userSaveDTO.setBindTenant(false); // 超管创建门店药师时 不绑定
        }
        Long userId = adminUserApi.saveOrUpdateUserByMobile(userSaveDTO);
        InquiryPharmacistDO pharmacist = getPharmacistByUserId(userId);
        if (pharmacist != null) {
            throw exception(INQUIRY_PHARMACIST_CREATE_EXISTS);
        }
        // 药师创建判断医生角色
        if (CollUtil.isNotEmpty(permissionApi.selectUserRoleByUserIdRoleCodes(userId, Set.of(RoleCodeEnum.DOCTOR.getCode())))) {
            throw exception(USER_HAS_ROLE_NO_ALLOW_ERROR, RoleCodeEnum.DOCTOR.getCode());
        }

        // 超管新增,直接处理分配系统药师角色
        permissionApi.assignUserRoleWithSystemRoleCode(userId, RoleCodeEnum.PHARMACIST.getCode());
        // save 药师
        InquiryPharmacistDO pharmacistDO = InquiryPharmacistConvert.INSTANCE.initConvertVO2DO(createReqVO);
        pharmacistDO.setAuditStatus(auditStatusEnum.getCode());
        pharmacistDO.setUserId(userId);
        inquiryPharmacistMapper.insert(pharmacistDO);
        // 写入资质证件信息
        saveProfessionIdentifications(createReqVO, pharmacistDO);
        return pharmacistDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInquiryPharmacistSystem(InquiryPharmacistSaveReqVO updateReqVO) {
        InquiryPharmacistDO pharmacistDO = validateInquiryPharmacistExists(updateReqVO.getId());
        // save user
        Long userId = adminUserApi.saveOrUpdateUserByMobile(InquiryPharmacistConvert.INSTANCE.convertUser(updateReqVO));
        InquiryPharmacistDO updateObj = InquiryPharmacistConvert.INSTANCE.convert(updateReqVO);
        updateObj.setUserId(userId);
        inquiryPharmacistMapper.updateById(updateObj);

        // 修改了药师类型，如果现在是门店药师:解绑所有门店关系; 如果现在是平台药师:绑定默认门店-1
        if (!Objects.equals(pharmacistDO.getPharmacistType(), updateReqVO.getPharmacistType())) {
            tenantUserRelationApi.reBindNewTenantUserRelation(TenantUserRelationBindDto.builder().userId(pharmacistDO.getUserId())
                .roleCode(RoleCodeEnum.PHARMACIST.getCode())
                .needClockIn(Objects.equals(updateReqVO.getPharmacistType(), PharmacistTypeEnum.PLATFORM.getCode()) ? CommonStatusEnum.ENABLE.getStatus() : null)
                .tenantIds(Objects.equals(updateReqVO.getPharmacistType(), PharmacistTypeEnum.DRUGSTORE.getCode()) ? null : Collections.singletonList(TenantConstant.DEFAULT_TENANT_ID)).build());
        }
        // 更新资质证件信息
        saveProfessionIdentifications(updateReqVO, updateObj);
    }

    /**
     * save资质信息 先删 再增
     *
     * @param saveReqVO    saveVo
     * @param pharmacistDO Do
     */
    private void saveProfessionIdentifications(InquiryPharmacistSaveReqVO saveReqVO, InquiryPharmacistDO pharmacistDO) {
        if (CollUtil.isEmpty(saveReqVO.getProfessionIdentifications()) || pharmacistDO == null) {
            return;
        }
        List<InquiryProfessionIdentificationDto> piSaveDTOs = saveReqVO.getProfessionIdentifications().stream()
            .peek(p -> {
                p.setPersonId(pharmacistDO.getId());
                p.setDoctorType(DoctorTypeEnum.PHARMACIST.getCode());
            }).collect(Collectors.toList());
        inquiryProfessionIdentificationApi.saveProfessionIdentifications(piSaveDTOs);
    }

    @Override
    public void deleteInquiryPharmacist(Long id) {
        // 校验存在
        InquiryPharmacistDO pharmacistDO = validateInquiryPharmacistExists(id);
        // 删除用户
        adminUserApi.deleteUserSystem(pharmacistDO.getUserId());
        // 删除
        inquiryPharmacistMapper.deleteById(id);
        // 删除资质信息
        inquiryProfessionIdentificationApi.deleteByGuidType(pharmacistDO.getId(), DoctorTypeEnum.PHARMACIST);
    }

    public InquiryPharmacistDO validateInquiryPharmacistExists(Long id) {
        final InquiryPharmacistDO inquiryPharmacistDO = inquiryPharmacistMapper.selectById(id);
        if (inquiryPharmacistDO == null) {
            throw exception(INQUIRY_PHARMACIST_NOT_EXISTS);
        }
        return inquiryPharmacistDO;
    }

    @Override
    public InquiryPharmacistRespVO getInquiryPharmacistVo(Long id) {
        InquiryPharmacistRespVO pharmacistRespVO = InquiryPharmacistConvert.INSTANCE.convert(validateInquiryPharmacistExists(id));
        // 查资质证件信息
        List<InquiryProfessionIdentificationDto> identifications = inquiryProfessionIdentificationApi.getProfessionIdentifications(pharmacistRespVO.getId(), DoctorTypeEnum.PHARMACIST);
        pharmacistRespVO.setProfessionIdentifications(identifications);
        // 电子签章图片url
        pharmacistRespVO.setSignatureUrl(inquiryUserSignatureInformationApi.getInquiryUserSignatureUrl(pharmacistRespVO.getUserId(), SignaturePlatformEnum.FDD));
        // 是否拥有指纹
        pharmacistRespVO.setHasFingerPrint(userFingerPrintApi.hasFingerPrint(pharmacistRespVO.getUserId()));

        return pharmacistRespVO;
    }

    /**
     * 根据guid获取药师基础信息
     *
     * @param id 药师id
     * @return 药师DO
     */
    @Override
    public InquiryPharmacistDO getInquiryPharmacistById(Long id) {
        return validateInquiryPharmacistExists(id);
    }

    @Override
    public InquiryPharmacistRespVO getInquiryPharmacistVoByMobile(String mobile) {
        AdminUserRespDTO user = adminUserApi.getUserByMobile(mobile);
        if (user != null) {
            InquiryPharmacistRespVO pharmacistRespVO = InquiryPharmacistConvert.INSTANCE.convert(inquiryPharmacistMapper.selectOne(InquiryPharmacistDO::getUserId, user.getId()));
            if (pharmacistRespVO != null) {
                // 查资质证件信息
                List<InquiryProfessionIdentificationDto> identifications = inquiryProfessionIdentificationApi.getProfessionIdentifications(pharmacistRespVO.getId(), DoctorTypeEnum.PHARMACIST);
                pharmacistRespVO.setProfessionIdentifications(identifications);
                // 电子签章图片url
                pharmacistRespVO.setSignatureUrl(inquiryUserSignatureInformationApi.getInquiryUserSignatureUrl(pharmacistRespVO.getUserId(), SignaturePlatformEnum.FDD));
            }
            return pharmacistRespVO;
        }
        return null;
    }

    @Override
    public PageResult<InquiryPharmacistRespVO> pagePharmacistSystem(InquiryPharmacistPageReqVO pageReqVO) {
        IPage<InquiryPharmacistRespVO> resultVo = inquiryPharmacistMapper.pagePharmacistSystem(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (CollUtil.isNotEmpty(resultVo.getRecords())) {
            // 组装user表状态
            List<Long> userIds = CollectionUtils.convertList(resultVo.getRecords(), InquiryPharmacistRespVO::getUserId);
            Map<Long, AdminUserRespDTO> userRespDTOMap = adminUserApi.getUserList(userIds).stream().collect(Collectors.toMap(AdminUserRespDTO::getId, Function.identity()));
            resultVo.getRecords().forEach(vo -> {
                vo.setStatus(Objects.equals(userRespDTOMap.get(vo.getUserId()).getStatus(), UserAccountStatusEnum.ENABLE.getCode()) ? UserAccountStatusEnum.ENABLE.getCode() : UserAccountStatusEnum.DISABLE.getCode());
            });
        }
        fillUserAccountStatus(resultVo.getRecords());
        return new PageResult(resultVo.getRecords(), resultVo.getTotal());
    }

    private void fillUserAccountStatus(List<InquiryPharmacistRespVO> records) {
        if (org.springframework.util.CollectionUtils.isEmpty(records)) {
            return;
        }
        // 查询用户
        List<AdminUserRespDTO> userDtos = adminUserApi.getUserList(records.stream().map(InquiryPharmacistRespVO::getUserId).distinct().toList());
        if (org.springframework.util.CollectionUtils.isEmpty(userDtos)) {
            return;
        }
        Map<Long, AdminUserRespDTO> userMap = userDtos.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, Function.identity()));
        records.forEach(vo -> {
            AdminUserRespDTO user = userMap.get(vo.getUserId());
            if (user != null) {
                vo.setUserAccountStatus(user.getStatus());
            }
        });
    }


    @Override
    public void auditInquiryPharmacist(InquiryPharmacistUpdateStatusReqVO updateReqVO) {
        InquiryPharmacistDO pharmacistDO = validateInquiryPharmacistExists(updateReqVO.getId());
        if (Objects.equals(pharmacistDO.getAuditStatus(), AuditStatusEnum.APPROVED.getCode())) {
            throw exception(INQUIRY_PHARMACIST_IS_AUDIT);
        }
        // 审核通过备注置为空,分配药师角色
        if (Objects.equals(updateReqVO.getAuditStatus(), AuditStatusEnum.APPROVED.getCode())) {
            updateReqVO.setRemark("");
            // permissionApi.assignUserRoleWithSystemRoleCode(pharmacistDO.getUserId(), RoleCodeEnum.PHARMACIST.getCode());
        }
        inquiryPharmacistMapper.updateById(InquiryPharmacistConvert.INSTANCE.convertStatus(updateReqVO));
    }


    @Override
    public PageResult<InquiryPharmacistRespVO> pagePharmacistStore(InquiryPharmacistPageReqVO pageReqVO) {
        // 查当前门店关联下有药师角色的所有员工
        List<TenantUserRelationDto> userRelations = tenantUserRelationApi.getTenantUserRelationsByRoleCode(RoleCodeEnum.PHARMACIST);
        if (CollUtil.isEmpty(userRelations)) {
            return new PageResult();
        }
        pageReqVO.setUserIds(CollectionUtils.convertList(userRelations, TenantUserRelationDto::getUserId));
        IPage<InquiryPharmacistRespVO> resultVo = inquiryPharmacistMapper.pagePharmacistStore(MyBatisUtils.buildPage(pageReqVO), pageReqVO);
        if (CollUtil.isNotEmpty(resultVo.getRecords())) {
            Map<Long, TenantUserRelationDto> userRelationDtoMap = userRelations.stream().collect(Collectors.toMap(TenantUserRelationDto::getUserId, Function.identity()));
            resultVo.getRecords().forEach(vo -> {
                vo.setStatus(userRelationDtoMap.get(vo.getUserId()).getStatus());
                vo.setTenantUserRelationId(userRelationDtoMap.get(vo.getUserId()).getId());
            });
        }
        fillUserAccountStatus(resultVo.getRecords());
        return new PageResult(resultVo.getRecords(), resultVo.getTotal());
    }

    // @Override
    // public PageResult<TenantDto> pagePharmacistBindStore(InquiryPharmacistPageReqVO pageReqVO) {
    //     // 数据在当前登录userId的可见门店列表内
    //     if (!TenantConstant.isSystemTenant()) {
    //         List<Long> tenantIds = TenantUtils.executeIgnore(() -> CollectionUtils.convertList(tenantUserRelationApi.getAvailableTenantListByUserId(SecurityFrameworkUtils.getLoginUserId()), TenantUserRelationDto::getTenantId));
    //         pageReqVO.setTenantIds(tenantIds);
    //         // 查询所属门店下的所有userId
    //         // tenantUserRelationApi.getTenantUserRelations(tenantIds);
    //
    //     }
    //     // 获取系统药师角色code
    //     pageReqVO.setRoleIds(roleApi.getSystemRoleIdByCodes(Collections.singletonList(RoleCodeEnum.PHARMACIST.getCode())));
    //     IPage<TenantDto> resultVo = TenantUtils.executeIgnore(() -> inquiryPharmacistMapper.pagePharmacistBindStore(MyBatisUtils.buildPage(pageReqVO), pageReqVO));
    //     return new PageResult(resultVo.getRecords(), resultVo.getTotal());
    // }

    /**
     * 根据药师编码查询药师信息
     *
     * @param pref
     * @return
     */
    @Override
    public InquiryPharmacistDO getPharmacistByPref(String pref) {
        return inquiryPharmacistMapper.selectOne(Wrappers.<InquiryPharmacistDO>lambdaQuery().eq(InquiryPharmacistDO::getPref, pref));
    }

    @Override
    public InquiryPharmacistDO getPharmacistByUserId(Long userId) {
        return inquiryPharmacistMapper.selectOne(Wrappers.<InquiryPharmacistDO>lambdaQuery().eq(InquiryPharmacistDO::getUserId, userId));
    }

    @Override
    public InquiryPharmacistDO getRequiredPharmacistByUserId(Long userId) {
        InquiryPharmacistDO pharmacistDO = inquiryPharmacistMapper.selectOne(Wrappers.<InquiryPharmacistDO>lambdaQuery().eq(InquiryPharmacistDO::getUserId, userId));
        if (pharmacistDO == null) {
            throw exception(INQUIRY_PHARMACIST_NOT_EXISTS);
        }
        return pharmacistDO;
    }

    /**
     * 根据药师编码查询药师信息
     *
     * @param userId
     * @return
     */
    @Override
    public InquiryPharmacistDO getRequiredApprovedPharmacistByUserId(Long userId) {
        InquiryPharmacistDO pharmacistDO = inquiryPharmacistMapper.selectOne(Wrappers.<InquiryPharmacistDO>lambdaQuery()
            .eq(InquiryPharmacistDO::getUserId, userId).eq(InquiryPharmacistDO::getAuditStatus, AuditStatusEnum.APPROVED.getCode()));
        if (pharmacistDO == null) {
            throw exception(INQUIRY_PHARMACIST_NOT_EXISTS);
        }
        return pharmacistDO;
    }

    @Override
    public InquiryPharmacistDO getRequiredOnLinePharmacistByUserId(Long userId) {
        InquiryPharmacistDO pharmacist = getRequiredApprovedPharmacistByUserId(userId);
        if (!Objects.equals(pharmacist.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            throw exception(INQUIRY_PHARMACIST_STATUS_ERROR, OnlineStatusEnum.OFFLINE.getRemark());
        }
        return pharmacist;
    }

    /**
     * 填充处理查询 超管/ 门店 药师数据条件
     *
     * @param pageReqVO
     */
    private void handleQueryPharmacistCondition(InquiryPharmacistPageReqVO pageReqVO) {

    }

    @Override
    public Long createInquiryPharmacistStore(InquiryPharmacistSaveReqVO createReqVO) {
        // 1.校验手机号是否可以在当前门店下创建
        adminUserApi.checkUserMobileCanCreateOnTenant(createReqVO.getMobile());
        // 2.门店创建 审核状态:待审核
        InquiryPharmacistDO pharmacistDO = createInquiryPharmacist(createReqVO, AuditStatusEnum.PENDING);
        // 3.发送延迟MQ-1min 自动审核通过
        pharmacistDelayAuditProducer.sendMessage(PharmacistAuditEvent.builder().msg(new PharmacistAuditMessageDTO().setPref(pharmacistDO.getPref()).setTenantId(TenantContextHolder.getRequiredTenantId())).build(),
            LocalDateTime.now().plusMinutes(1));
        return pharmacistDO.getId();
    }


    @Override
    public void updateInquiryPharmacistStore(InquiryPharmacistSaveReqVO updateReqVO) {
        // 门店修改药师，改为待审核
        updateReqVO.setAuditStatus(AuditStatusEnum.PENDING.getCode());
        updateInquiryPharmacistSystem(updateReqVO);
        // 发送延迟MQ-1min 自动审核通过
        pharmacistDelayAuditProducer.sendMessage(PharmacistAuditEvent.builder().msg(new PharmacistAuditMessageDTO().setPref(updateReqVO.getPref()).setTenantId(TenantContextHolder.getRequiredTenantId())).build(),
            LocalDateTime.now().plusSeconds(30));
    }


    @Override
    public List<TenantUserRelationDto> getPharmacistBindStoreList() {
        // 获取当前用户 在哪些门店，有药师角色
        return tenantUserRelationApi.getUserTenantRelationsByRoleCode(RoleCodeEnum.PHARMACIST);
    }

    @Override
    public List<InquiryPharmacistDO> getPharmacistList(PharmacistQueryDto qualifications) {
        return inquiryPharmacistMapper.queryByCondition(qualifications);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startReceipt() {
        InquiryPharmacistDO pharmacist = getRequiredApprovedPharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        if (Objects.equals(pharmacist.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            return true; // 兼容多端出诊
            // throw exception(INQUIRY_PHARMACIST_OFFLINE_ONLINE, OnlineStatusEnum.ONLINE.getRemark());
        }

        // if (!Objects.equals(pharmacist.getAuditStatus(), AuditStatusEnum.APPROVED.getCode())) {
        //     throw exception(INQUIRY_PHARMACIST_STATUS_ERROR, "未审核通过");
        // }

        AdminUserRespDTO userRespDTO = adminUserApi.getTenantUser();
        if (!CommonStatusEnum.isEnable(userRespDTO.getStatus()) || !CommonStatusEnum.isEnable(userRespDTO.getRelationStatus())) {
            throw exception(USER_IS_DISABLE, userRespDTO.getNickname());
        }
        // 出诊校验CA认证免签
        boolean caAuthFreeSign = inquirySignatureCaAuthApi.isCaAuthFreeSign(pharmacist.getUserId(), SignaturePlatformEnum.FDD);
        String signatureUrl = inquiryUserSignatureInformationApi.getInquiryUserSignatureUrl(pharmacist.getUserId(), SignaturePlatformEnum.SELF);
        if (Objects.equals(pharmacist.getDrawnSign(), DrawnSignEnum.Y.getCode()) && StringUtils.isBlank(signatureUrl)) {
            throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL);
        }
        if (!caAuthFreeSign && !Objects.equals(pharmacist.getDrawnSign(), DrawnSignEnum.Y.getCode())) {
            throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_FREE_SIGN);
        }
        boolean start = inquiryPharmacistMapper.updateById(pharmacist.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode())) > 0;
        // 记录打卡
        tenantUserRelationApi.tenantUserClockIn(new TenantUserClockInDto().setClockInType(ClockInTypeEnum.CLOCK_IN).setUserIp(ServletUtils.getClientIP()).setUserAgent(ServletUtils.getUserAgent()));
        return start;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopReceipt() {
        InquiryPharmacistDO pharmacist = getPharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        if (Objects.equals(pharmacist.getOnlineStatus(), OnlineStatusEnum.OFFLINE.getCode())) {
            return true; // 兼容多端出诊
            // throw exception(INQUIRY_PHARMACIST_OFFLINE_ONLINE, OnlineStatusEnum.OFFLINE.getRemark());
        }

        boolean stop = inquiryPharmacistMapper.updateById(pharmacist.setOnlineStatus(OnlineStatusEnum.OFFLINE.getCode())) > 0;
        // 记录打卡
        tenantUserRelationApi.tenantUserClockIn(new TenantUserClockInDto().setClockInType(ClockInTypeEnum.CLOCK_OUT).setUserIp(ServletUtils.getClientIP()).setUserAgent(ServletUtils.getUserAgent()));

        return stop;
    }

    @Override
    public InquiryPharmacistRespVO getPharmacistInfo() {
        InquiryPharmacistDO pharmacist = getPharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        if (pharmacist == null) {
            throw exception(INQUIRY_PHARMACIST_NOT_EXISTS);
        }
        return getInquiryPharmacistVo(pharmacist.getId());
    }

    @Override
    public Boolean logOffCheck() {
        InquiryPharmacistDO pharmacist = getPharmacistByUserId(WebFrameworkUtils.getLoginUserId());
        if (pharmacist == null) {
            throw exception(INQUIRY_PHARMACIST_NOT_EXISTS);
        }
        if (ObjectUtil.equals(pharmacist.getOnlineStatus(), OnlineStatusEnum.ONLINE.getCode())) {
            throw exception(INQUIRY_PHARMACIST_ONLINE_LOGOFF_ERROR);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateInquiryPharmacistInfo(PharmacistUpdateInfoVo updateInfoVo) {
        InquiryPharmacistDO pharmacist = getRequiredPharmacistByUserId(Optional.ofNullable(updateInfoVo.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        InquiryPharmacistDO updateObj = InquiryPharmacistConvert.INSTANCE.convertUpdate(updateInfoVo).setId(pharmacist.getId());
        return inquiryPharmacistMapper.updateById(updateObj) > 0;
    }
}