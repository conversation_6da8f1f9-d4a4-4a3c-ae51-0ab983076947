package com.xyy.saas.inquiry.pharmacist.server.api.audit;

import com.xyy.saas.inquiry.pharmacist.api.audit.InquiryPrescriptionAuditApi;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.RemotePrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/26 11:49
 */
// @Service
@DubboService
public class InquiryPrescriptionAuditApiImpl implements InquiryPrescriptionAuditApi {


    @Resource
    private InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    @Override
    public void remotePrescriptionPushAuditPool(RemotePrescriptionAuditDto auditDto) {
        inquiryPrescriptionAuditService.remotePrescriptionOperateAuditPool(auditDto.setAudit(true));
    }

    @Override
    public void remotePrescriptionRemoveAuditPool(RemotePrescriptionAuditDto auditDto) {
        inquiryPrescriptionAuditService.remotePrescriptionOperateAuditPool(auditDto);
    }


    @Override
    public void remotePrescriptionBatchRemoveAuditPool(RemotePrescriptionAuditDto auditDto) {
        inquiryPrescriptionAuditService.remotePrescriptionBatchRemoveAuditPool(auditDto);
    }
}
