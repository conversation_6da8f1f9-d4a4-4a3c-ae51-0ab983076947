package com.xyy.saas.inquiry.signature.server.api.platform;

import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.platform.InquirySignaturePlatformApi;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/11 11:51
 */
// @Service
@DubboService
public class InquirySignaturePlatformApiImpl implements InquirySignaturePlatformApi {

    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    @Override
    public SignaturePlatformEnum getMaster() {
        return SignaturePlatformEnum.fromCode(inquirySignaturePlatformService.getMaster());
    }
}
