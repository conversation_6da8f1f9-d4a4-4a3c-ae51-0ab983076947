package com.xyy.saas.inquiry.signature.server.config.template;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate.InquiryPrescriptionTemplateDO;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import com.xyy.saas.inquiry.util.ThreadPoolManager;
import com.xyy.saas.inquiry.util.TimeWatchUtil;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 处方笺模板CacheInit 启动时将所有处方笺模板url加载到缓存byte[]
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class InquiryPrescriptionTemplateCacheInit implements CommandLineRunner {

    @Autowired
    private InquiryPrescriptionTemplateService inquiryPrescriptionTemplateService;


    @Override
    public void run(String... args) {
        TimeWatchUtil.excute(() -> {
            List<InquiryPrescriptionTemplateDO> templateDOS = inquiryPrescriptionTemplateService.loadAllTemplate();
            if (CollUtil.isEmpty(templateDOS)) {
                return;
            }
            // 等待所有CompletableFuture完成，收集并处理异常 （指定线程池）
            CompletableFuture.allOf(
                templateDOS.stream().map(templateDO ->
                        CompletableFuture.runAsync(
                            () -> inquiryPrescriptionTemplateService.getPrescriptionTemplate4Cache(templateDO.getId()),
                            ThreadPoolManager.getInstance() )
                    ).toArray(CompletableFuture[]::new) )
                .thenRun(() -> log.info("All prescription templates have been cached successfully."))
                .exceptionally(ex -> {
                    log.error("Error occurred while caching prescription templates: {}", ex.getMessage(), ex);
                    return null;
                })
                .join(); // 阻塞当前线程直到所有任务完成
        }, "inquiry prescription template cache init");
    }

}