package com.xyy.saas.inquiry.signature.server.api.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.api.prescription.InquirySignaturePrescriptionApi;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:52
 */
// @Service
@DubboService
@Slf4j
public class InquirySignaturePrescriptionApiImpl implements InquirySignaturePrescriptionApi {

    @Resource
    private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;

    @Override
    public CommonResult<?> issuePrescription(PrescriptionSignatureInitDto prescriptionSignatureInitDto) {
        return inquirySignaturePrescriptionService.issuePrescription(prescriptionSignatureInitDto);
    }

    @Override
    public CommonResult<?> auditPrescription(PrescriptionSignatureAuditDto prescriptionSignatureAuditDto) {
        return inquirySignaturePrescriptionService.auditPrescription(prescriptionSignatureAuditDto);
    }
}
