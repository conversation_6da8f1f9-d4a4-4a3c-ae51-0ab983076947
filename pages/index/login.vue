<!-- 微信公众号的登录回调页 -->
<template>
	<view class='normal-login-container' v-if='isShow'>
		<view class='logo-content'>
			<view class='login-logo'>
				<image class='img-logo' src='/static/images/login/logo.png'></image>
			</view>
			<view class='login-tab'>
				<view @click='loginTab.changeTab(0)' :class="['tab-item', loginTab.tab === 0 ? 'active' : '']">密码登录
				</view>
				<view @click='loginTab.changeTab(1)' :class="['tab-item', loginTab.tab === 1 ? 'active' : '']">验证码登录
				</view>
			</view>
		</view>
		<view class='login-form-content'>

			<!-- 账户密码登录 -->
			<UserForm ref="userForm" v-if='loginTab.tab === 0' :formData='loginForm'
				@setFormData='loginForm.changeData' />
			<!-- 手机号登录 -->
			<MobileForm ref="mobileForm" v-if='loginTab.tab === 1' :formData='loginForm'
				@setFormData='loginForm.changeData' />

			<view v-if='loginTab.tab === 0' class='action-btn'>

				<button :disabled="!loginForm.username||!loginForm.password" @click='handleLogin'
					:class="(!loginForm.username||!loginForm.password)?'disabled-button':''"
					class='login-btn cu-btn block bg-green lg'>登录</button>
			</view>
			<view v-if='loginTab.tab === 1' class='action-btn'>

				<button :disabled="!loginForm.mobile||!loginForm.code" @click='handleLogin'
					:class="(!loginForm.mobile||!loginForm.code)?'disabled-button':''"
					class='login-btn cu-btn block bg-green lg'>登录</button>
			</view>
			<view class='forget'>
				<view class='term-box'>
					<div class='term-content'>
						<up-checkbox shape='circle' activeColor='#00B955' name='agree' v-model:checked='check'
							style="transform:scale(0.8)" usedAlone></up-checkbox>
						<text> 已阅读并同意</text>
						<text class='text-green' @click="goToPrivacyOrService(1)">《服务条款》</text>
						<text class='text-green' @click="goToPrivacyOrService(2)">《隐私政策》</text>
					</div>
					<text v-if='loginTab.tab === 0' class='text-grey'>忘记密码</text>
				</view>
			</view>


			<view class='other-login'>
				<!--  -->
				<view v-show="false" @click="iosHandleTest" class='title'>
					<view class='line'></view>
					<view class='t'>其他登录方式</view>
				</view>
				<view v-show="false" class='icon-other'>
					<view class='item'>
						<image src='/static/images/logo_icon3.png'></image>
						智慧脸登录
					</view>
					<view class='item'>
						<image src='/static/images/heye-logo.png'></image>
						药帮忙登录
					</view>
				</view>
				<view class='help-tel'>
					<view class='tel'>问诊业务咨询 ************</view>
					<!-- <view class='tel'>其他咨询 ************</view> -->
				</view>
			</view>
		</view>
		<!-- <button @click="toTest" style="position: fixed;top:30px;left:30px;z-index: 999;">测试</button> -->
		<view class="privacy-popup" v-if="firstFalg">
			<view class="privacy-box">
				<view class="title">
					温馨提示
				</view>
				<view class="content">
					<p class="mg-t-sm">我们依据最新的法律要求，更新了隐私权政策，特地向您推送本提示。</p>
					<p class="mg-t-sm">我们一直采取行业领先的安全防护措施来保护您的信息安全。我们会根据您使用服务的
						具体功能需要收集使用信息（可能涉及账户、交易、设备等相关信息）。我们不会向任何第三方提供您的信息，除非得到您的授权。若我们将信息用于您未授权用途或目的，我们会事先再次征求您的同意。</p>
					<p class="mg-t-sm">您可以阅读我们完整的<span style="color:rgb(18 193 98);"
							@click="goToPrivacyOrService(2)">《隐私政策》</span>和<span style="color:rgb(18 193 98);"
							@click="goToPrivacyOrService(1)">《荷叶问诊用户服务协议》</span>了解我们的承诺。</p>
				</view>
				<view class="footer">
					<view @click="privacyHandle(0)">不同意并退出</view>
					<view @click="privacyHandle(1)">同意</view>
				</view>
			</view>
		</view>

	</view>
	<up-modal class="auto-modal" :show="showModel" title="提示" contentTextAlign='center' width='600rpx'
		:showConfirmModel='true' :showCancelButton="true" @cancel="showModel=false" @confirm="confirmNotification"
		confirm-text="确定" confirm-color="#00B955">
		<view class='ss-flex ss-flex-col ss-justify-center ss-align-center'>
			<view>您好！为了及时向您推送重要功能提醒（如新订单提示、订单超时关闭、系统通知等），我们需要获取 “消息通知” 权限。

			</view>
			<view>
				开启后，您将第一时间收到专属服务通知，不错过任何关键信息；
			</view>
			<view class='mb20'>
				若选择暂不开启，您仍可正常使用 APP，但部分实时提醒功能可能受到影响。
			</view>
		</view>
	</up-modal>
	<!-- <NoticeBox/> -->
</template>

<script setup>
	import permision from "@/sheep/helper/trtc/permission.js";
	import $store from '@/sheep/store';
	import sheep from '@/sheep';
	import {
		onLoad,
		onInit,
		onShow
	} from '@dcloudio/uni-app';
	import {
		ref,
		reactive,
		onMounted,
		onBeforeMount
	} from 'vue';

	import LoginApi from '@/sheep/api/login/index.js';
	import UserForm from './components/Login/UserForm.vue';
	import MobileForm from './components/Login/MobileForm.vue';
	import imApi from '@/sheep/api/inquiry/im';
	import TrtcCloud from '@/sheep/helper/trtc/index';
	import NoticeBox from "@/components/NoticeBox/index.vue"
	const userForm = ref(null)
	const mobileForm = ref(null)
	let isShow = ref(false);
	const showModel = ref(false)
	onShow(() => {

	})
	onLoad((options) => {

		// 解绑账号
		const appStore = $store('app');
		const userStore = $store('user');
		if (appStore.mobilePush) {
			console.log("appStore.mobilePush", appStore.mobilePush)
			appStore.mobilePush.unBindAccount() // 退出登陆解绑
			setTimeout(() => {
				const isSetNotification = uni.getStorageSync("isSetNotification")
				if (uni.getSystemInfoSync().platform === "android" && !isSetNotification) {
					const checkNotificationRes = appStore.mobilePush.checkNotification()
					console.log("checkNotificationRes", checkNotificationRes)
					showModel.value = !checkNotificationRes
					if (showModel.value) {
						uni.setStorageSync("isSetNotification", 1)
					}
				}
			}, 1500)
		}

		if (options.isLogOut == 1) {
			const userStore = $store('user');
			isShow.value = true;
			userStore.isLogin = false
			uni.setStorageSync('accessToken', '');
			uni.setStorageSync('loginUser', '');
		} else if (uni.getStorageSync('accessToken')) {
			console.log('异常情况进入登录页')
			uni.navigateTo({
				url: '/pages/index/mainWebview/mainWebview'
			});
		} else {
			isShow.value = true;
		}
		const chatStore = $store('chat');
		const imIsLogin = uni.getStorageSync('imIsLogin');

		if (Number(imIsLogin) === 1) {
			chatStore.imObj.onLogout()
		}

		// 设置历史账号密码
		let accountHistoryList = uni.getStorageSync('accountHistoryList');
		if (accountHistoryList) {
			accountHistoryList = JSON.parse(accountHistoryList)
			let data = accountHistoryList[0]
			loginForm.username = data.username
			loginForm.password = data.password
		}


	});

	function confirmNotification() {
		const appStore = $store('app');
		if (appStore.mobilePush) {
			appStore.mobilePush.toNotification()
			showModel.value = false
		}
	}
	// 更改登录模式
	const loginTab = reactive({
		tab: 0,
		changeTab(t = 0) {
			loginTab.tab = t;
			console.info('mobileForm', mobileForm.value)
			// if (mobileForm.value) {
			// 	mobileForm.value.clearInput()
			// }
			// if (userForm.value) {
			// 	userForm.value.clearInput()
			// }
			console.info('userForm', userForm.value)
			console.info("loginTab.tab", loginTab.tab)
			if (t == 1) { // 准备切换到验证码tab，并且在账号tab填写了账号，且是个手机号，就默认填到验证码tab的手机号输入框里面
				loginForm.changeData({
					mobile: loginForm.username,
					code: ""
				});
			}
			if (t == 0) {
				loginForm.changeData({
					username: loginForm.mobile,
					code: ""
				});
			}
		},
	});

	// 协议条款
	const check = ref(false);
	const loginForm = reactive({
		username: '',
		password: '',
		mobile: '',
		code: '',
		changeData(data = {}) {
			Object.assign(loginForm, data);
		},
	});


	const vailForm = () => {
		if (loginTab.tab === 0) {
			if (!loginForm.username) {
				sheep.$helper.toast('请输入您的账号');
				return false;
			}
			if (!loginForm.password) {
				sheep.$helper.toast('请输入您的密码');
				return false;
			}
		} else if (loginTab.tab === 1) {
			if (!loginForm.code) {
				sheep.$helper.toast('请输入验证码');
				return false;
			}
			if (!loginForm.mobile) {
				sheep.$helper.toast('请输入您的手机号');
				return false;
			}
		}
		if (!check.value) {
			sheep.$helper.toast('请先阅读并同意用户协议');
			return false;
		}


		return true;
	};

	const handleLogin = async () => {
		uni.getNetworkType({
			success: (res) => {
				console.log(res.networkType);
				if (res.networkType == 'none') {
					uni.showToast({
						icon: "none",
						title: "没有网络连接,请检查网络设置"
					})
					return
				}
				toLogin()
			}
		});
		async function toLogin() {
			if (!vailForm()) return;

			let res = {};

			switch (loginTab.tab) {
				case 0:
					res = await LoginApi.login({
						username: loginForm.username,
						password: loginForm.password,
					});
					break;
				case 1:
					res = await LoginApi.smsLogin({
						mobile: loginForm.mobile,
						code: loginForm.code,
					});
					break;
			}
			// 存储登陆账号--start
			let accountHistoryList = uni.getStorageSync('accountHistoryList');
			if (loginTab.tab == 0) {
				if (accountHistoryList) {
					accountHistoryList = JSON.parse(accountHistoryList)
					accountHistoryList.unshift({
						username: loginForm.username,
						password: loginForm.password,
					})
					uni.setStorageSync('accountHistoryList', JSON.stringify(accountHistoryList));
				} else {
					accountHistoryList = [{
						username: loginForm.username,
						password: loginForm.password,
					}]
					uni.setStorageSync('accountHistoryList', JSON.stringify(accountHistoryList));
				}
			}

			// 存储登陆账号--end
			const data = res.data;
			if (data && data.accessToken) {
				uni.setStorageSync('isgoback', 0)
				uni.setStorageSync('accessToken', data.accessToken);
				uni.setStorageSync('loginUser', data);
				// let url = '/pages/index/mainWebview/mainWebview';
				// 存在多门店，跳转到选择门店页面
				// if (data.multiTenant) {
				let url = `/pages/index/mainWebview/mainWebview?params=${encodeURI('/store/selectStore')}`;
				// }
				uni.navigateTo({
					url
				});
				// setTimeout(async () => {
				// 	// 在此处登录IM
				// 	const chatStore = $store('chat');

				// 	const imIsLogin = uni.getStorageSync('imIsLogin');
				// 	console.info("imIsLogin:", imIsLogin)
				// 	if (Number(imIsLogin) === 0) {
				// 		chatStore.startConnect()
				// 	}
				// }, 500)
			}

		}
	};

	const pwdLogin = async () => {};

	const loginSuccess = async () => {};

	// 跳转隐私政策
	const goToPrivacyOrService = (type) => {
		let h5Url = ''
		const deviceType = getDeviceType()
		if (type == 1) { // 服务条款
			h5Url = deviceType == "IOS" ? "/consultation-ios" : '/consultation'
		}
		if (type == 2) { // 隐私政策
			h5Url = deviceType == "IOS" ? "/privacy-ios" : '/privacy'
		}
		// "\/pages\/index\/mainWebview\/mainWebview\?params\=\/consultation"
		let url = `/pages/index/mainWebview/mainWebview?params=${encodeURI(h5Url)}`;
		uni.navigateTo({
			url
		});
		uni.setStorageSync('isgoback', 1)
	}

	function toTest() {
		let url = `/pages/test/index`;
		uni.navigateTo({
			url
		});
	}

	const firstFalg = ref(false)
	onMounted(() => {
		const flag = uni.getStorageSync('first_falg');
		console.log('flag', Boolean(flag))
		var platform = uni.getSystemInfoSync().platform;
		if (!Boolean(flag) && platform == 'ios') {
			firstFalg.value = true
		}
	})

	function privacyHandle(type) {
		if (type == 1) {
			firstFalg.value = false
			uni.setStorageSync('first_falg', 1)
		}
		if (type == 0) {
			if (uni.getSystemInfoSync().platform === 'ios') {
				plus.ios.import("UIApplication").sharedApplication().performSelector("exit");
			} else {

				plus.runtime.quit();
			}
		}
	}

	function iosHandleTest() {
		const privactAlert = uni.requireNativePlugin('DCloud-PrivactAlert')
		const res = privactAlert.invokeNativeMethod()
		alert(res)
	}

	function getDeviceType() {
		let deviceType = ""
		//APP
		// #ifdef APP-PLUS
		const port = uni.getSystemInfoSync().platform
		if (port === 'android') {
			deviceType = 'Android'
		}
		if (port === 'ios') {
			deviceType = 'IOS'
		}
		// #endif
		//h5
		// #ifdef H5
		deviceType = 'H5';
		// #endif

		//小程序
		// #ifdef MP-WEIXIN
		deviceType = 'MP-WEIXIN';
		// #endif
		return deviceType
	}
</script>

<style>


</style>
<style lang='scss' scoped>
	.normal-login-container {
		width: 100%;
		background-image: linear-gradient(153deg, #c9ffe2 0%, #e4fcf3 22%, #f5fafd 34%, #ffffff 40%);

		.logo-content {
			font-size: 21px;
			padding: 10% 30px 0 30px;

			image {
				border-radius: 50%;
				width: 53px;
				height: 56px;
			}

			.title {
				margin-left: 10px;
			}

			.login-tab {
				display: flex;
				gap: 33px;
				padding: 30px 0 10px 0;
				font-weight: 600;
				font-family: PingFangSC;
			}

			.tab-item {
				color: #999999;
				position: relative;
				padding-bottom: 10px;
				cursor: pointer;
				transition: 0.4s;

				&.active {
					color: #333333;
					scale: 1.1;

					&::after {
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translate(-50%);
						content: '';
						width: 100%;
						height: 4px;
						border-radius: 5px;
						background-color: #01b956;
					}
				}
			}
		}

		.term-box {
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 12px;
			color: #222222;
			margin-top: 12px;

			.term-content {
				display: flex;
				align-items: center;
			}

			.text-green {
				color: #00B955
			}

			.text-grey {
				color: #8799a3 !important;
			}

			.uni-radio-input {
				margin-top: -3px;
				margin-left: -8px;
			}
		}

		.login-form-content {
			text-align: center;
			margin: 20px auto;
			// margin-top: 3%;
			padding: 0 30px;

			.input-item {
				margin: 20px auto;
				height: 45px;
				line-height: 45px;
				border-radius: 8px;
				border: 1px solid #b8bec2;

				.icon {
					font-size: 38rpx;
					margin-left: 10px;
					color: #999;
				}

				.input {
					width: 100%;
					font-size: 14px;
					line-height: 20px;
					text-align: left;
					padding-left: 15px;
				}
			}

			.login-btn {
				margin-top: 40rpx;
				height: 90rpx;
				background: #00B955;
				color: $white !important;
				border: unset;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			.disabled-button {
				background: #C5CAD6;
			}

			.reg {
				margin-top: 30rpx;
			}

			.xieyi {
				color: #333;
				margin-top: 30rpx;
			}

			.login-code {
				height: 38px;
				float: right;

				.login-code-img {
					height: 38px;
					position: absolute;
					margin-left: 10px;
					width: 200rpx;
				}
			}
		}
	}

	.other-login {
		padding-top: 80rpx;

		.title {
			font-size: 16px;
			color: #999999;
			height: 50rpx;
			margin-bottom: 40rpx;
			position: relative;

			.t {
				padding: 0 10px;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				background: #fff;
				font-size: 12px;
			}

			.line {
				position: absolute;
				top: 50%;
				left: 0;
				right: 0;
				height: 1px;
				background-color: #e0e0e0;
			}
		}

		.icon-other {
			display: flex;
			justify-content: center;
			align-items: center;
			gap: 90px;

			.item {
				font-size: 12px;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 10px;
				flex-direction: column;
				color: #222222;

				image {
					width: 40px;
					height: 40px;
				}
			}
		}

		.help-tel {
			font-size: 12px;
			color: #999999;
			text-align: center;
			margin-top: 30px;

			.tel+.tel {
				margin-top: 5px;
			}
		}

		image {
			width: 50px;
			height: 50px;
		}
	}

	.text {
		margin-left: -111px;
		margin-top: 28px;
	}

	.uni-input-input {
		box-shadow: 0 0 0px 1000px #fff inset;
	}

	.uni-input-input:-internal-autofill-selected {
		background-color: white !important;
		background-image: none !important;
		color: rgb(0, 0, 0) !important;
		box-shadow: inset 0 0 0 1000px white !important;
	}

	.uni-input-input:-webkit-autofill {
		box-shadow: 0 0 0px 1000px white inset !important;
	}

	.uni-input-input:-webkit-autofill:focus {
		box-shadow: 0 0 0px 1000px white inset !important;
	}

	.privacy-popup {
		position: fixed;
		z-index: 999999;
		left: 0;
		right: 0;
		bottom: 0;
		top: 0;
		background-color: #fff;
		display: flex;
		align-items: center;
		justify-content: center;

		&::after {
			position: absolute;
			content: '';
			display: block;
			background: #ededed;
			left: 0;
			bottom: 0;
			right: 0;
			top: 0;
			z-index: -1;
		}

		.privacy-box {
			width: 70%;
			padding: 30rpx 30rpx 0rpx;
			background: #fff;
			border-radius: 14rpx;

			.title {
				font-family: PingFangSC-Medium;
				font-weight: 500;
				font-size: 30rpx;
				color: #111334;
				text-align: center;
				margin-bottom: 60rpx;
			}

			.content {
				margin: 30rpx 0rpx;
			}

			.mg-t-sm {
				margin-top: 30rpx;
			}

			.footer {
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-top: 1rpx solid #eee;
				padding: 30rpx 0rpx 0rpx;

				>view {
					width: 50%;
					text-align: center;
					padding-bottom: 30rpx;

					&:nth-child(1) {
						border-right: .5rpx solid #eee;
					}

					&:nth-child(2) {
						border-left: .5rpx solid #eee;
						color: #00B955;
					}
				}
			}
		}
	}
</style>