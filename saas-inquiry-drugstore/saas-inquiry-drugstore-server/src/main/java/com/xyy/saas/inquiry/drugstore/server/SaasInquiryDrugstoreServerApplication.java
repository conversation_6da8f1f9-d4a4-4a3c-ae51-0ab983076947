package com.xyy.saas.inquiry.drugstore.server;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;


@EnableDubbo
@EnableDiscoveryClient
@SpringBootApplication(exclude = {
//        YudaoTenantAutoConfiguration.class,
//        YudaoSecurityAutoConfiguration.class,
//        YudaoSecurityAutoConfiguration.class,
}
)
public class SaasInquiryDrugstoreServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(SaasInquiryDrugstoreServerApplication.class, args);
    }


}
