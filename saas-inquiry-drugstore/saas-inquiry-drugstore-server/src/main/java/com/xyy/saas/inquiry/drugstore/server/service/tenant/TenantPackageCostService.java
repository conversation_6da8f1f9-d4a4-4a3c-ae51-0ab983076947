package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantChangeCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantDeductCostDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.cost.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.cost.TenantPackageCostSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 门店问诊套餐额度 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantPackageCostService {

    /**
     * 初始化问诊套餐
     *
     * @param tenantPackageCostDto
     * @return
     */
    void saveTenantPackageCost(@Valid TenantPackageCostDto tenantPackageCostDto);

    /**
     * 校验门店问诊套餐额度
     *
     * @param bizTypeEnum        业务类型
     * @param inquiryWayTypeEnum 问诊类型
     * @return
     */
    List<TenantPackageCostDO> isValidTenantPackage(BizTypeEnum bizTypeEnum, InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum);

    /**
     * 查询门店可用套餐包
     *
     * @param bizTypeEnum
     * @return 存在的问诊方式s
     */
    List<TenantPackageCostDO> availableInquiryWayTypes(BizTypeEnum bizTypeEnum);

    /**
     * 扣减用户套餐额度 - 默认门店类型
     *
     * @param inquiryWayTypeEnum 问诊类型
     * @return TenantDeductCostDto
     */
    TenantDeductCostDto deductTenantCost(InquiryWayTypeEnum inquiryWayTypeEnum, InquiryBizTypeEnum inquiryBizTypeEnum, String bizId);


    /**
     * 问诊额度回退
     *
     * @param changeCostDto 额度changeCostDto
     */
    void reBackTenantCost(TenantChangeCostDto changeCostDto);


    /**
     * 创建门店问诊套餐额度
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantPackageCostRelation(@Valid TenantPackageCostSaveReqVO createReqVO);

    /**
     * 更新门店问诊套餐额度
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantPackageCostRelation(@Valid TenantPackageCostSaveReqVO updateReqVO);

    /**
     * 删除门店问诊套餐额度
     *
     * @param id 编号
     */
    void deleteTenantPackageCostRelation(Long id);


    /**
     * 获得门店问诊套餐额度分页
     *
     * @param pageReqVO 分页查询
     * @return 门店问诊套餐额度分页
     */
    PageResult<TenantPackageCostDO> getTenantPackageCostRelationPage(TenantPackageCostReqVO pageReqVO);

    /**
     * 查询门店问诊套餐包信息
     *
     * @param pageReqVO
     * @return
     */
    List<TenantPackageCostDO> queryTenantPackageCostByCondition(TenantPackageCostReqVO pageReqVO);

    /**
     * 套餐额度状态变更
     *
     * @param packageCostDto 套餐额度信息
     */
    void updateTenantPackageCostStatus(TenantPackageCostDto packageCostDto);

    /**
     * 变更额度
     *
     * @param packageCostDto 额度dto
     */
    void changeTenantPackageCost(TenantPackageCostDto packageCostDto);

    /**
     * 判断是否有平台审方 生效的套餐 仅查套餐类型是药店问诊时。远程审方忽略
     *
     * @return
     */
    boolean validPlatformReviewCost();


    TenantPackageCostDto getTenantPackageCostById(Long costId);

    TenantPackageCostDto getTenantPackageCostByLogBizId(String bizId, CostRecordTypeEnum costRecordTypeEnum);

    Map<String, TenantPackageCostDto> getTenantPackageCostByLogBizIds(List<String> bizIds, CostRecordTypeEnum costRecordTypeEnum);
}