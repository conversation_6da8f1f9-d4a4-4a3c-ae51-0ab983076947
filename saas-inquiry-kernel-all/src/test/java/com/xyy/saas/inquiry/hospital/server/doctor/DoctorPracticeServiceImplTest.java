package com.xyy.saas.inquiry.hospital.server.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticePageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticeSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorPracticeDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorPracticeMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorPracticeServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_PRACTICE_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;


/**
 * {@link DoctorPracticeServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(DoctorPracticeServiceImpl.class)
public class DoctorPracticeServiceImplTest extends BaseIntegrationTest {

    @Resource
    private DoctorPracticeServiceImpl doctorPracticeService;

    @Resource
    private DoctorPracticeMapper doctorPracticeMapper;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateDoctorPractice_success() {
        // 准备参数
        DoctorPracticeSaveReqVO createReqVO = randomPojo(DoctorPracticeSaveReqVO.class);
        createReqVO.setId(null);
        createReqVO.setFirstPracticeLevel(1);
        // 调用
        Long doctorPracticeId = doctorPracticeService.createDoctorPractice(createReqVO);
        // 断言
        assertNotNull(doctorPracticeId);
        // 校验记录的属性是否正确
        DoctorPracticeDO doctorPractice = doctorPracticeMapper.selectById(doctorPracticeId);
        assertPojoEquals(createReqVO, doctorPractice, "id");
    }

    @Test
    public void testUpdateDoctorPractice_success() {
        // mock 数据
        DoctorPracticeDO dbDoctorPractice = randomPojo(DoctorPracticeDO.class);
        dbDoctorPractice.setFirstPracticeLevel(1);
        doctorPracticeMapper.insert(dbDoctorPractice);// @Sql: 先插入出一条存在的数据
        // 准备参数
        DoctorPracticeSaveReqVO updateReqVO = randomPojo(DoctorPracticeSaveReqVO.class, o -> {
            o.setId(dbDoctorPractice.getId()); // 设置更新的 ID
        });
        updateReqVO.setFirstPracticeLevel(1);
        // 调用
        doctorPracticeService.updateDoctorPractice(updateReqVO);
        // 校验是否更新正确
        DoctorPracticeDO doctorPractice = doctorPracticeMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, doctorPractice);
    }

    @Test
    public void testUpdateDoctorPractice_notExists() {
        // 准备参数
        DoctorPracticeSaveReqVO updateReqVO = randomPojo(DoctorPracticeSaveReqVO.class);
        updateReqVO.setFirstPracticeLevel(1);
        // 调用, 并断言异常
        assertServiceException(() -> doctorPracticeService.updateDoctorPractice(updateReqVO), DOCTOR_PRACTICE_NOT_EXISTS);
    }

    @Test
    public void testDeleteDoctorPractice_success() {
        // mock 数据
        DoctorPracticeDO dbDoctorPractice = randomPojo(DoctorPracticeDO.class);
        dbDoctorPractice.setFirstPracticeLevel(1);
        doctorPracticeMapper.insert(dbDoctorPractice);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbDoctorPractice.getId();

        // 调用
        doctorPracticeService.deleteDoctorPractice(id);
        // 校验数据不存在了
        assertNull(doctorPracticeMapper.selectById(id));
    }

    @Test
    public void testDeleteDoctorPractice_notExists() {
        // 准备参数
//        Long id = randomLong();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> doctorPracticeService.deleteDoctorPractice(id), DOCTOR_PRACTICE_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetDoctorPracticePage() {
        // mock 数据
        DoctorPracticeDO dbDoctorPractice = randomPojo(DoctorPracticeDO.class, o -> { // 等会查询到
            o.setFirstPracticeName("五和医院");
            o.setFirstPracticeLevel(2);
//            o.setDeptGuid("fsadsafsfsafs");
//            o.setTitleCode("10");
//            o.setTitleName("主任医师");
            o.setTitleNo("1451265122542");
            o.setTitleTime(LocalDateTime.now());
            o.setStartPracticeTime(LocalDateTime.now());
            o.setEndPracticeDate(LocalDateTime.now());
            o.setProfessionalNo("5324221");
            o.setProfessionalTime(LocalDateTime.now());
            o.setQualificationNo("256344211");
            o.setQualificationTime(LocalDateTime.now());
            o.setCreateTime(LocalDateTime.now());
        });
        doctorPracticeMapper.insert(dbDoctorPractice);
        // 测试 guid 不匹配
        // 测试 firstPracticeName 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setFirstPracticeName(null)));
        // 测试 firstPracticeLevel 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setFirstPracticeLevel(null)));
        // 测试 deptGuid 不匹配
//        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setDeptGuid(null)));
        // 测试 titleCode 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setTitleCode(null)));
        // 测试 titleName 不匹配
//        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setTitleName("副主任医师")));
        // 测试 titleNo 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setTitleNo(null)));
        // 测试 titleTime 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setTitleTime(null)));
        // 测试 startPracticeTime 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setStartPracticeTime(null)));
        // 测试 endPracticeDate 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setEndPracticeDate(null)));
        // 测试 professionalNo 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setProfessionalNo(null)));
        // 测试 professionalTime 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setProfessionalTime(null)));
        // 测试 qualificationNo 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setQualificationNo(null)));
        // 测试 qualificationTime 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setQualificationTime(null)));
        // 测试 createTime 不匹配
        doctorPracticeMapper.insert(cloneIgnoreId(dbDoctorPractice, o -> o.setCreateTime(null)));
        // 准备参数
        DoctorPracticePageReqVO reqVO = new DoctorPracticePageReqVO();
        reqVO.setTitleName("副主任医师");
        // 调用
        PageResult<DoctorPracticeDO> pageResult = doctorPracticeService.getDoctorPracticePage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
    }

}