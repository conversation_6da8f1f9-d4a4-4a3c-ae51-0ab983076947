package com.xyy.saas.inquiry.hospital.server.doctor;

import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.BaseIntegrationTest;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorServiceImpl;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * {@link InquiryDoctorServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryDoctorServiceImpl.class)
public class InquiryDoctorServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryDoctorServiceImpl inquiryDoctorService;

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @BeforeEach
    public void before() {
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
//        TenantContextHolder.setTenantId();
    }

    @Test
    public void testCreateInquiryDoctor_success() {
        // 准备参数
        InquiryDoctorSaveReqVO createReqVO = randomPojo(InquiryDoctorSaveReqVO.class);
        createReqVO.setId(null);
        createReqVO.setSex(1);
//        createReqVO.setCooperation(0);
//        createReqVO.setEnvTag(1);
        // 调用
        InquiryDoctorDO doctor = inquiryDoctorService.createInquiryDoctor(createReqVO);
        // 断言
        assertNotNull(doctor);
        // 校验记录的属性是否正确
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(doctor.getId());
        assertPojoEquals(createReqVO, inquiryDoctor, "id");
    }

    @Test
    public void testUpdateInquiryDoctor_success() {
        // mock 数据
        InquiryDoctorDO dbInquiryDoctor = randomPojo(InquiryDoctorDO.class);
        dbInquiryDoctor.setId(null);
        dbInquiryDoctor.setSex(1);
        dbInquiryDoctor.setCooperation(0);
        // dbInquiryDoctor.setEnvTag(1);
        inquiryDoctorMapper.insert(dbInquiryDoctor);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryDoctorSaveReqVO updateReqVO = randomPojo(InquiryDoctorSaveReqVO.class, o -> {
            o.setId(dbInquiryDoctor.getId()); // 设置更新的 ID
        });
        updateReqVO.setSex(1);
//        updateReqVO.setCooperation(0);
//        updateReqVO.setEnvTag(1);
        // 调用
        inquiryDoctorService.updateInquiryDoctor(updateReqVO);
        // 校验是否更新正确
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryDoctor);
    }

    @Test
    public void testUpdateInquiryDoctor_notExists() {
        // 准备参数
        InquiryDoctorSaveReqVO updateReqVO = randomPojo(InquiryDoctorSaveReqVO.class);
        updateReqVO.setSex(1);
//        updateReqVO.setCooperation(0);
//        updateReqVO.setEnvTag(1);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryDoctorService.updateInquiryDoctor(updateReqVO), INQUIRY_DOCTOR_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryDoctor_success() {
        // mock 数据
        InquiryDoctorDO dbInquiryDoctor = randomPojo(InquiryDoctorDO.class);
        dbInquiryDoctor.setSex(1);
        dbInquiryDoctor.setCooperation(0);
        // dbInquiryDoctor.setEnvTag(1);
        inquiryDoctorMapper.insert(dbInquiryDoctor);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryDoctor.getId();

        // 调用
        inquiryDoctorService.deleteInquiryDoctor(id);
        // 校验数据不存在了
        assertNull(inquiryDoctorMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryDoctor_notExists() {
        // 准备参数
//        Long id = randomLong();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> inquiryDoctorService.deleteInquiryDoctor(id), INQUIRY_DOCTOR_NOT_EXISTS);
    }

    @Test
    public void testGetInquiryDoctorPage() {
        // mock 数据
        InquiryDoctorDO dbInquiryDoctor = randomPojo(InquiryDoctorDO.class, o -> { // 等会查询到
            o.setName("张三");
            o.setSex(1);
            o.setIdCard("******************");
            o.setUserId(11610L);
            o.setAuditStatus(1);
            o.setCooperation(0);
            o.setStartInquiryTime(LocalDateTime.now());
            o.setEndInquiryTime(LocalDateTime.now());
            // o.setEnvTag(0);
            o.setPhoto("GDSFGDSSGSGD");
            o.setBiography("这个是测试的个人简介");
            o.setProfessionalDec("擅长开处方");
            o.setJobType(1);
            o.setPrescriptionPasswordStatus(false);
            o.setPrescriptionPassword("");
            o.setDisable(false);
            o.setCreateTime(LocalDateTime.now());
        });
        inquiryDoctorMapper.insert(dbInquiryDoctor);
        // 测试 guid 不匹配
        // 测试 name 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setName("李四")));
        // 测试 sex 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setSex(2)));
        // 测试 idCard 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setIdCard("******************")));
        // 测试 userId 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setUserId(1157448L)));
        // 测试 auditStatus 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setAuditStatus(2)));
        // 测试 cooperation 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setCooperation(1)));
        // 测试 startInquiryTime 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setStartInquiryTime(null)));
        // 测试 endInquiryTime 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setEndInquiryTime(null)));
        // // 测试 envTag 不匹配
        // inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setEnvTag(1)));
        // 测试 photo 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPhoto("照骗")));
        // 测试 biography 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setBiography("这是个人简介看看吧")));
        // 测试 professionalDec 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setProfessionalDec("擅长问诊开方")));
        // 测试 jobType 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setJobType(2)));
        // 测试 prescriptionPasswordStatus 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPrescriptionPasswordStatus(true)));
        // 测试 prescriptionPassword 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPrescriptionPassword("123456")));
        // 测试 disable 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setDisable(true)));
        // 测试 createTime 不匹配
        inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryDoctorPageReqVO reqVO = new InquiryDoctorPageReqVO();
        reqVO.setName("李四");
        // 调用
        PageResult<InquiryDoctorRespVO> pageResult = inquiryDoctorService.getInquiryDoctorPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        // assertPojoEquals(dbInquiryDoctor, pageResult.getList().get(0));
    }

}