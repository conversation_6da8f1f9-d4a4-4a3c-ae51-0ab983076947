//package com.xyy.saas.inquiry.physician.server.doctor;
//
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
//import com.xyy.saas.inquiry.physician.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
//import com.xyy.saas.inquiry.physician.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
//import com.xyy.saas.inquiry.physician.server.dal.dataobject.doctor.InquiryDoctorDO;
//import com.xyy.saas.inquiry.physician.server.dal.mysql.doctor.InquiryDoctorMapper;
//import com.xyy.saas.inquiry.physician.server.service.doctor.InquiryDoctorServiceImpl;
//import jakarta.annotation.Resource;
//import org.junit.jupiter.api.Disabled;
//import org.junit.jupiter.api.Test;
//import org.springframework.context.annotation.Import;
//
//import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
//import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
//import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
//import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
//import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
//import static com.xyy.saas.inquiry.physician.enums.ErrorCodeConstants.INQUIRY_DOCTOR_NOT_EXISTS;
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * {@link InquiryDoctorServiceImpl} 的单元测试类
// *
// * <AUTHOR>
// */
//@Import(InquiryDoctorServiceImpl.class)
//public class InquiryDoctorServiceImplTest extends BaseDbUnitTest {
//
//    @Resource
//    private InquiryDoctorServiceImpl inquiryDoctorService;
//
//    @Resource
//    private InquiryDoctorMapper inquiryDoctorMapper;
//
//    @Test
//    public void testCreateInquiryDoctor_success() {
//        // 准备参数
//        InquiryDoctorSaveReqVO createReqVO = randomPojo(InquiryDoctorSaveReqVO.class).setId(null);
//
//        // 调用
//        Integer inquiryDoctorId = inquiryDoctorService.createInquiryDoctor(createReqVO);
//        // 断言
//        assertNotNull(inquiryDoctorId);
//        // 校验记录的属性是否正确
//        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(inquiryDoctorId);
//        assertPojoEquals(createReqVO, inquiryDoctor, "id");
//    }
//
//    @Test
//    public void testUpdateInquiryDoctor_success() {
//        // mock 数据
//        InquiryDoctorDO dbInquiryDoctor = randomPojo(InquiryDoctorDO.class);
//        inquiryDoctorMapper.insert(dbInquiryDoctor);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        InquiryDoctorSaveReqVO updateReqVO = randomPojo(InquiryDoctorSaveReqVO.class, o -> {
//            o.setId(dbInquiryDoctor.getId()); // 设置更新的 ID
//        });
//
//        // 调用
//        inquiryDoctorService.updateInquiryDoctor(updateReqVO);
//        // 校验是否更新正确
//        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectById(updateReqVO.getId()); // 获取最新的
//        assertPojoEquals(updateReqVO, inquiryDoctor);
//    }
//
//    @Test
//    public void testUpdateInquiryDoctor_notExists() {
//        // 准备参数
//        InquiryDoctorSaveReqVO updateReqVO = randomPojo(InquiryDoctorSaveReqVO.class);
//
//        // 调用, 并断言异常
//        assertServiceException(() -> inquiryDoctorService.updateInquiryDoctor(updateReqVO), INQUIRY_DOCTOR_NOT_EXISTS);
//    }
//
//    @Test
//    public void testDeleteInquiryDoctor_success() {
//        // mock 数据
//        InquiryDoctorDO dbInquiryDoctor = randomPojo(InquiryDoctorDO.class);
//        inquiryDoctorMapper.insert(dbInquiryDoctor);// @Sql: 先插入出一条存在的数据
//        // 准备参数
//        Integer id = dbInquiryDoctor.getId();
//
//        // 调用
//        inquiryDoctorService.deleteInquiryDoctor(id);
//       // 校验数据不存在了
//       assertNull(inquiryDoctorMapper.selectById(id));
//    }
//
//    @Test
//    public void testDeleteInquiryDoctor_notExists() {
//        // 准备参数
////        Integer id = randomIntegerId();
////
////        // 调用, 并断言异常
////        assertServiceException(() -> inquiryDoctorService.deleteInquiryDoctor(id), INQUIRY_DOCTOR_NOT_EXISTS);
//    }
//
//    @Test
//    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
//    public void testGetInquiryDoctorPage() {
//       // mock 数据
//       InquiryDoctorDO dbInquiryDoctor = randomPojo(InquiryDoctorDO.class, o -> { // 等会查询到
//           o.setGuid(null);
//           o.setName(null);
//           o.setSex(null);
//           o.setIdCard(null);
//           o.setPhone(null);
//           o.setUserId(null);
//           o.setAuditStatus(null);
//           o.setCooperation(null);
//           o.setLastInquiryTime(null);
//           o.setStartInquiryTime(null);
//           o.setEndInquiryTime(null);
//           o.setEnvTag(null);
//           o.setPhoto(null);
//           o.setBiography(null);
//           o.setProfessionalDec(null);
//           o.setJobType(null);
//           o.setPrescriptionPasswordStatus(null);
//           o.setPrescriptionPassword(null);
//           o.setDisable(null);
//           o.setCreateTime(null);
//       });
//       inquiryDoctorMapper.insert(dbInquiryDoctor);
//       // 测试 guid 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setGuid(null)));
//       // 测试 name 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setName(null)));
//       // 测试 sex 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setSex(null)));
//       // 测试 idCard 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setIdCard(null)));
//       // 测试 phone 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPhone(null)));
//       // 测试 userId 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setUserId(null)));
//       // 测试 auditStatus 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setAuditStatus(null)));
//       // 测试 cooperation 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setCooperation(null)));
//       // 测试 lastInquiryTime 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setLastInquiryTime(null)));
//       // 测试 startInquiryTime 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setStartInquiryTime(null)));
//       // 测试 endInquiryTime 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setEndInquiryTime(null)));
//       // 测试 envTag 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setEnvTag(null)));
//       // 测试 photo 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPhoto(null)));
//       // 测试 biography 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setBiography(null)));
//       // 测试 professionalDec 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setProfessionalDec(null)));
//       // 测试 jobType 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setJobType(null)));
//       // 测试 prescriptionPasswordStatus 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPrescriptionPasswordStatus(null)));
//       // 测试 prescriptionPassword 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setPrescriptionPassword(null)));
//       // 测试 disable 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setDisable(null)));
//       // 测试 createTime 不匹配
//       inquiryDoctorMapper.insert(cloneIgnoreId(dbInquiryDoctor, o -> o.setCreateTime(null)));
//       // 准备参数
//       InquiryDoctorPageReqVO reqVO = new InquiryDoctorPageReqVO();
//       reqVO.setGuid(null);
//       reqVO.setName(null);
//       reqVO.setSex(null);
//       reqVO.setIdCard(null);
//       reqVO.setPhone(null);
//       reqVO.setUserId(null);
//       reqVO.setAuditStatus(null);
//       reqVO.setCooperation(null);
//       reqVO.setLastInquiryTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setStartInquiryTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setEndInquiryTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//       reqVO.setEnvTag(null);
//       reqVO.setPhoto(null);
//       reqVO.setBiography(null);
//       reqVO.setProfessionalDec(null);
//       reqVO.setJobType(null);
//       reqVO.setPrescriptionPasswordStatus(null);
//       reqVO.setPrescriptionPassword(null);
//       reqVO.setDisable(null);
//       reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
//
//       // 调用
//       PageResult<InquiryDoctorDO> pageResult = inquiryDoctorService.getInquiryDoctorPage(reqVO);
//       // 断言
//       assertEquals(1, pageResult.getTotal());
//       assertEquals(1, pageResult.getList().size());
//       assertPojoEquals(dbInquiryDoctor, pageResult.getList().get(0));
//    }
//
//}