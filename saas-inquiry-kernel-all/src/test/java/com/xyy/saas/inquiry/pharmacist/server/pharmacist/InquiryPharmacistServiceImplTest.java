package com.xyy.saas.inquiry.pharmacist.server.pharmacist;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_PHARMACIST_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pharmacist.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist.InquiryPharmacistMapper;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link InquiryPharmacistServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(InquiryPharmacistServiceImpl.class)
public class InquiryPharmacistServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryPharmacistServiceImpl inquiryPharmacistService;

    @Resource
    private InquiryPharmacistMapper inquiryPharmacistMapper;

    @Test
    public void testCreateInquiryPharmacist_success() {
        // 准备参数
        InquiryPharmacistSaveReqVO createReqVO = randomPojo(InquiryPharmacistSaveReqVO.class).setId(null);

        // 调用
        // Long inquiryPharmacistId = inquiryPharmacistService.createInquiryPharmacistSystem(createReqVO);
        // // 断言
        // assertNotNull(inquiryPharmacistId);
        // 校验记录的属性是否正确
        // InquiryPharmacistDO inquiryPharmacist = inquiryPharmacistMapper.selectById(inquiryPharmacistId);
        // assertPojoEquals(createReqVO, inquiryPharmacist, "id");
    }

    @Test
    public void testUpdateInquiryPharmacist_success() {
        // mock 数据
        InquiryPharmacistDO dbInquiryPharmacist = randomPojo(InquiryPharmacistDO.class);
        inquiryPharmacistMapper.insert(dbInquiryPharmacist);// @Sql: 先插入出一条存在的数据
        // 准备参数
        InquiryPharmacistSaveReqVO updateReqVO = randomPojo(InquiryPharmacistSaveReqVO.class, o -> {
            o.setId(dbInquiryPharmacist.getId()); // 设置更新的 ID
        });

        // 调用
        inquiryPharmacistService.updateInquiryPharmacistSystem(updateReqVO);
        // 校验是否更新正确
        InquiryPharmacistDO inquiryPharmacist = inquiryPharmacistMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, inquiryPharmacist);
    }

    @Test
    public void testUpdateInquiryPharmacist_notExists() {
        // 准备参数
        InquiryPharmacistSaveReqVO updateReqVO = randomPojo(InquiryPharmacistSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> inquiryPharmacistService.updateInquiryPharmacistSystem(updateReqVO), INQUIRY_PHARMACIST_NOT_EXISTS);
    }

    @Test
    public void testDeleteInquiryPharmacist_success() {
        // mock 数据
        InquiryPharmacistDO dbInquiryPharmacist = randomPojo(InquiryPharmacistDO.class);
        inquiryPharmacistMapper.insert(dbInquiryPharmacist);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryPharmacist.getId();

        // 调用
        inquiryPharmacistService.deleteInquiryPharmacist(id);
        // 校验数据不存在了
        assertNull(inquiryPharmacistMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryPharmacist_notExists() {
        // 准备参数
//        Integer id = randomIntegerId();
//
//        // 调用, 并断言异常
//        assertServiceException(() -> inquiryPharmacistService.deleteInquiryPharmacist(id), INQUIRY_PHARMACIST_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInquiryPharmacistPage() {
        // mock 数据
        InquiryPharmacistDO dbInquiryPharmacist = randomPojo(InquiryPharmacistDO.class, o -> { // 等会查询到
            o.setUserId(null);
            o.setName(null);
            o.setSex(null);
            o.setIdCard(null);
            o.setMobile(null);
            o.setAuditStatus(null);
            o.setPhoto(null);
            o.setBiography(null);
            o.setQualification(null);
            o.setPharmacistType(null);
            o.setSchool(null);
            o.setCreateTime(null);
        });
        inquiryPharmacistMapper.insert(dbInquiryPharmacist);
        // 测试 guid 不匹配
        // 测试 userId 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setUserId(null)));
        // 测试 name 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setName(null)));
        // 测试 sex 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setSex(null)));
        // 测试 idCard 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setIdCard(null)));
        // 测试 phone 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setMobile(null)));
        // 测试 auditStatus 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setAuditStatus(null)));
        // 测试 photo 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setPhoto(null)));
        // 测试 biography 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setBiography(null)));
        // 测试 qualification 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setQualification(null)));
        // 测试 pharmacistType 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setPharmacistType(null)));
        // 测试 school 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setSchool(null)));
        // 测试 disable 不匹配
        // 测试 createTime 不匹配
        inquiryPharmacistMapper.insert(cloneIgnoreId(dbInquiryPharmacist, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryPharmacistPageReqVO reqVO = new InquiryPharmacistPageReqVO();
        reqVO.setUserId(null);
        reqVO.setName(null);
        reqVO.setSex(null);
        reqVO.setIdCard(null);
        reqVO.setMobile(null);
        reqVO.setAuditStatus(null);
        reqVO.setPhoto(null);
        reqVO.setBiography(null);
        reqVO.setQualification(null);
        reqVO.setPharmacistType(null);
        reqVO.setSchool(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        final PageResult<InquiryPharmacistRespVO> pageResult = inquiryPharmacistService.pagePharmacistStore(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbInquiryPharmacist, pageResult.getList().get(0));
    }

}