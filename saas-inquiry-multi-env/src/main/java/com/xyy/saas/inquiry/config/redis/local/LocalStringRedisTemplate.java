package com.xyy.saas.inquiry.config.redis.local;

import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * 本地内存实现的 StringRedisTemplate
 * 使用 ConcurrentHashMap 模拟 Redis 字符串操作
 */
public class LocalStringRedisTemplate extends StringRedisTemplate {
    
    private final Map<String, String> memoryStore = new ConcurrentHashMap<>();
    private final Map<String, Long> expirationMap = new ConcurrentHashMap<>();
    
    public LocalStringRedisTemplate() {
        setKeySerializer(new StringRedisSerializer());
        setValueSerializer(new StringRedisSerializer());
        setHashKeySerializer(new StringRedisSerializer());
        setHashValueSerializer(new StringRedisSerializer());
    }
    
    @Override
    public ValueOperations<String, String> opsForValue() {
        return new LocalStringValueOperations(this);
    }
    
    @Override
    public Boolean hasKey(String key) {
        if (isExpired(key)) {
            memoryStore.remove(key);
            expirationMap.remove(key);
            return false;
        }
        return memoryStore.containsKey(key);
    }
    
    @Override
    public Boolean delete(String key) {
        memoryStore.remove(key);
        expirationMap.remove(key);
        return true;
    }
    
    @Override
    public Long delete(Collection<String> keys) {
        long count = 0;
        for (String key : keys) {
            if (hasKey(key)) {
                delete(key);
                count++;
            }
        }
        return count;
    }
    
    public void putValue(String key, String value) {
        memoryStore.put(key, value);
    }
    
    public void putValue(String key, String value, long timeout, TimeUnit unit) {
        memoryStore.put(key, value);
        long expireTime = System.currentTimeMillis() + unit.toMillis(timeout);
        expirationMap.put(key, expireTime);
    }
    
    public String getValue(String key) {
        if (isExpired(key)) {
            memoryStore.remove(key);
            expirationMap.remove(key);
            return null;
        }
        return memoryStore.get(key);
    }
    
    public Long increment(String key, long delta) {
        String value = getValue(key);
        long newValue = (value != null ? Long.parseLong(value) : 0) + delta;
        putValue(key, String.valueOf(newValue));
        return newValue;
    }
    
    private boolean isExpired(String key) {
        Long expireTime = expirationMap.get(key);
        return expireTime != null && System.currentTimeMillis() > expireTime;
    }

    /**
     * Set time to live for given {@code key}.
     *
     * @param key     must not be {@literal null}.
     * @param timeout must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @throws IllegalArgumentException if the timeout is {@literal null}.
     * @since 2.3
     */
    @Override
    public Boolean expire(String key, Duration timeout) {
        if (!hasKey(key)) {
            return false;
        }
        long expireTime = System.currentTimeMillis() + timeout.toMillis();
        expirationMap.put(key, expireTime);
        return true;
    }

    /**
     * Set the expiration for given {@code key} as a {@literal date} timestamp.
     *
     * @param key      must not be {@literal null}.
     * @param expireAt must not be {@literal null}.
     * @return {@literal null} when used in pipeline / transaction.
     * @throws IllegalArgumentException if the instant is {@literal null} or too large to represent as a {@code Date}.
     * @since 2.3
     */
    @Override
    public Boolean expireAt(String key, Instant expireAt) {
        if (!hasKey(key)) {
            return false;
        }
        expirationMap.put(key, expireAt.toEpochMilli());
        return true;
    }

    /**
     * Create {@code key} using the {@code serializedValue}, previously obtained using {@link #dump(Object)}.
     *
     * @param key        must not be {@literal null}.
     * @param value      must not be {@literal null}.
     * @param timeToLive
     * @param unit       must not be {@literal null}.
     * @see <a href="https://redis.io/commands/restore">Redis Documentation: RESTORE</a>
     */
    @Override
    public void restore(String key, byte[] value, long timeToLive, TimeUnit unit) {
        throw new UnsupportedOperationException("restore not implemented in local mode");
    }
}
