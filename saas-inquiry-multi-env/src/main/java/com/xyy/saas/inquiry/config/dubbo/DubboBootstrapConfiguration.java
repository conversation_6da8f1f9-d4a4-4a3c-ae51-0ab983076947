package com.xyy.saas.inquiry.config.dubbo;

import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.ServiceConfig;
import org.apache.dubbo.config.bootstrap.DubboBootstrap;
import org.apache.dubbo.config.bootstrap.builders.ServiceBuilder;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Service;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * @Desc dubbo配置
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/10 下午5:17
 */
@Slf4j
@ConditionalOnProperty(prefix = "dubbo.bootstrap", value = "enabled")
@EnableConfigurationProperties(DubboBootstrapProperties.class)
public class DubboBootstrapConfiguration
    implements ApplicationListener<ContextRefreshedEvent>, ApplicationContextAware, Ordered {

    private ApplicationContext applicationContext;

    private final Set<Class<?>> serviceClasses = new HashSet<>();

    @SuppressWarnings("rawtypes")
    private final List<ServiceConfig> serviceConfigList = new ArrayList<>();

    @Autowired
    private DubboBootstrapProperties dubboBootstrapProperties;

    @Override
    public void setApplicationContext(@Nonnull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public DubboBootstrapConfiguration() {
        log.info("【多环境】DubboBootstrapConfiguration 初始化成功！");
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (ObjectUtils.nullSafeEquals(applicationContext, event.getSource())) {
            try {
                log.info(">>>>>>>>>>>>>>>>>>>> Dubbo services starting...");
                DubboBootstrap bootstrap = DubboBootstrap.getInstance()
                    .services(serviceConfigList);

                bootstrap.start();
                log.info("<<<<<<<<<<<<<<<<<<<< Dubbo services started successfully!");
            } catch (Exception e) {
                log.error("<<<<<<<<<<<<<<<<<<<< Dubbo services started failed: {}", e.getMessage(), e);
                // 根据需要决定是否重新抛出异常或采取其他措施
                throw e;
            }
        }
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE - 1;
    }

    @Bean
    public BeanPostProcessor dubboServiceBeanPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessBeforeInitialization(@Nonnull Object bean, @Nonnull String beanName) throws BeansException {
                if (ServiceClassChecker.isApiImplClass(bean, dubboBootstrapProperties)) {
                    serviceClasses.add(bean.getClass());
                }
                return bean;
            }

            @Override
            public Object postProcessAfterInitialization(@Nonnull Object bean, @Nonnull String beanName) throws BeansException {
                registerAsDubboService(bean);
                return bean;
            }

            private void registerAsDubboService(Object bean) {
                final Object targetObject = ServiceClassChecker.getTargetObject(bean);
                if (targetObject == null || !serviceClasses.contains(targetObject.getClass())) {
                    return;
                }
                // 设置暴露的服务接口和服务实现
                Class<?> interfaceClass = targetObject.getClass().getInterfaces()[0];
                log.info("Add Dubbo interface into Registering Services: {}", interfaceClass.getName());
                serviceConfigList.add(ServiceBuilder.newBuilder()
                    .interfaceClass(interfaceClass)
                    .ref(targetObject)
                    .build());
            }
        };
    }

    public static class ServiceClassChecker {

        private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher(".");

        public static boolean isApiImplClass(Object bean, DubboBootstrapProperties properties) {
            Object targetObject = getTargetObject(bean);
            if (Objects.isNull(targetObject)) {
                return false;
            }

            Class<?> targetClass = targetObject.getClass();

            // 检查是否有 @Service 注解且类名以 "ApiImpl" 结尾
            if (!targetClass.isAnnotationPresent(Service.class) || !targetClass.getSimpleName().endsWith("ApiImpl")) {
                return false;
            }

            // 检查包路径是否符合配置
            return isPackageMatched(targetClass.getName(), properties);
        }

        /**
         * 检查类的包路径是否符合配置要求
         * 支持 Ant 风格的路径匹配模式，如：
         * - com.xyy.saas.inquiry.** (匹配所有子包)
         * - com.xyy.saas.inquiry.*.service (匹配单层子包下的service包)
         * - com.xyy.saas.inquiry.hospital.service.* (匹配指定包下的所有类)
         *
         * @param className 完整类名
         * @param properties 配置属性
         * @return 是否符合要求
         */
        private static boolean isPackageMatched(String className, DubboBootstrapProperties properties) {
            // 如果没有配置 basePackage，则不进行包路径过滤
            if (CollectionUtils.isEmpty(properties.getBasePackage())) {
                return true;
            }

            // 检查是否在基础包路径中（使用 Ant 风格匹配）
            boolean inBasePackage = properties.getBasePackage().stream()
                .anyMatch(basePackage -> matchesPattern(className, basePackage));

            if (!inBasePackage) {
                return false;
            }

            // 检查是否在排除包路径中（使用 Ant 风格匹配）
            if (!CollectionUtils.isEmpty(properties.getExcludePackage())) {
                boolean inExcludePackage = properties.getExcludePackage().stream()
                    .anyMatch(excludePackage -> matchesPattern(className, excludePackage));

                if (inExcludePackage) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 使用 Ant 风格路径匹配器检查类名是否匹配模式
         *
         * @param className 完整类名
         * @param pattern 匹配模式
         * @return 是否匹配
         */
        private static boolean matchesPattern(String className, String pattern) {
            // 如果模式不包含通配符，则进行精确匹配或前缀匹配
            if (!pattern.contains("*") && !pattern.contains("?")) {
                return className.equals(pattern) || className.startsWith(pattern + ".");
            }

            // 使用 AntPathMatcher 进行模式匹配
            return PATH_MATCHER.match(pattern, className);
        }

        public static Object getTargetObject(Object proxy) {
            if (!AopUtils.isAopProxy(proxy)) {
                return proxy;
            }
            return AopProxyUtils.getSingletonTarget(proxy);
        }
    }
}
