# Dubbo Bootstrap 配置示例
dubbo:
  bootstrap:
    # 启用 Dubbo Bootstrap 自动配置
    enabled: true
    # 基础包路径，只有在这些包路径下的类才会被暴露为 Dubbo 服务
    base-package:
      - com.xyy.saas.inquiry.hospital.service
      - com.xyy.saas.inquiry.patient.service
      - com.xyy.saas.inquiry.pharmacist.service
    # 排除包路径，在这些包路径下的类不会被暴露为 Dubbo 服务
    exclude-package:
      - com.xyy.saas.inquiry.hospital.service.internal
      - com.xyy.saas.inquiry.patient.service.test
