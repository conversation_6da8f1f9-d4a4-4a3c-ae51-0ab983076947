<template>
  <!-- 列表 -->
   <ContentWrap>
    <div class="header_btn">
      <div class="header_btn_left">
        <el-button type="primary" @click="allConfigVisible=true">全局设置</el-button>
        <div style="margin-left: 10px;" v-if="procInquiryCompliance && isNumber(procInquiryComplianceForPatientAgeGe)">限制问诊年龄 大于等于{{ procInquiryComplianceForPatientAgeGe }}岁</div>
        <div style="margin-left: 10px;" v-if="procInquiryCompliance && isNumber(procInquiryComplianceForPatientAgeGe)">小于{{procInquiryComplianceForPatientAgeLt }}岁</div>
      </div>
      <el-button type="primary" @click="rulesDialogVisible=true">新增规则</el-button>
    </div>
   </ContentWrap>
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" >
      <el-table-column type="index" width="50"/>

      <el-table-column label="已配置生效区域" align="center" prop="province"  :show-overflow-tooltip="true">
        <template #default="scope">
          {{scope.row.province}} /   {{scope.row.city}} /   {{scope.row.area}}
        </template>
      </el-table-column>

      <el-table-column label="问诊年龄限制" align="center" prop="procInquiryComplianceForPatientAgeGe">
        <template #default="scope">
          {{  (scope.row.procInquiryComplianceForPatientAgeGe != null  ? ('大于等于'+scope.row.procInquiryComplianceForPatientAgeGe+'岁') : '') +  ( scope.row.procInquiryComplianceForPatientAgeLt != null ? '且小于'+scope.row.procInquiryComplianceForPatientAgeLt+'岁' : '')}}
        </template>
      </el-table-column>
      <el-table-column label="妊娠哺乳是否可发起问诊" align="center" prop="procInquiryComplianceAllowForPregnancyLactation">
        <template #default="scope">
          {{scope.row.procInquiryComplianceAllowForPregnancyLactation ? '是':'否'}}
        </template>
      </el-table-column>

       <el-table-column label="操作" align="center"  fixed="right">
        <template #default="scope">
          <el-button type="primary" @click="handelEdit(scope.row)">编辑</el-button>
          <el-button  @click="handelDelete(scope.row)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

  <!-- 全局设置弹框 -->
  <Dialog v-model="allConfigVisible" title="全局设置" width="40%" :close-on-click-modal="false">
      <div class="dialog_body">
        <el-form>
            <el-form-item label="">
              <el-radio-group v-model="allConfigForm.procInquiryCompliance">
                <el-radio :value="true">开启</el-radio>
                <el-radio :value="false">关闭</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="患者年龄限制">
              <div class="limit_cla">
                <div class="align_center">
                  <div class="label">大于等于</div>
                  <el-input-number v-model.number="allConfigForm.procInquiryComplianceForPatientAgeGe" min="0" max="150" placeholder="请输入" />
                </div>
                <div class="align_center">
                  <div class="label">且小于</div>
                  <el-input-number v-model.number="allConfigForm.procInquiryComplianceForPatientAgeLt" min="0" max="150" placeholder="请输入" />
                </div>
              </div>
            </el-form-item>
          </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="allConfigSubmit">确 定</el-button>
        <el-button @click="allConfigCancel">取 消</el-button>
      </template>
  </Dialog>
  <!-- 新增编辑规则弹框 -->
  <Dialog v-model="rulesDialogVisible" :title="configDialogTitle" width="40%" :close-on-click-modal="false">
      <div class="dialog_body">
        <el-form>
          <el-form-item label="门店区域">
             <el-cascader v-model="rulesForm.areas" :options="areaList" :props="multipleProps"  collapse-tags clearable />
          </el-form-item>
          <el-form-item label="患者年龄限制">
            <div class="limit_cla">
              <div class="align_center">
                <div class="label">大于等于</div>
                <el-input v-model.number="rulesForm.procInquiryComplianceForPatientAgeGe" placeholder="请输入" />
              </div>
              <div class="align_center">
                <div class="label">且小于</div>
                <el-input v-model.number="rulesForm.procInquiryComplianceForPatientAgeLt" placeholder="请输入" />
              </div>
            </div>
          </el-form-item>
          <el-form-item label="妊娠哺乳是否可发起问诊">
              <el-radio-group v-model="rulesForm.procInquiryComplianceAllowForPregnancyLactation">
                <el-radio :value="true">允许发起问诊</el-radio>
                <el-radio :value="false">禁止发起问诊</el-radio>
              </el-radio-group>
            </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="rulesSubmitFn">确 定</el-button>
        <el-button @click="rulesCancelFn">取 消</el-button>
      </template>
  </Dialog>
  </ContentWrap>

</template>
<script lang="ts" setup>
import * as SettingApi from "@/api/inquiry/setting";
import {globalConfigSave, globalConfigQuery} from '@/api/inquiry-option-config'
import {strictlyProps, treeToIds,multipleProps} from "@/utils/tree";

import * as AreaApi from '@/api/system/area'
import {isNumber} from "@/utils/is";
import {deleteAreaConfig} from "@/api/inquiry/setting";
import { cloneDeep } from 'lodash-es'
const message = useMessage() // 消息弹窗

const props = defineProps({
  active:{
    type:[],
    default:0
  },
  optionType:{
    type:Number,
    default:0
  },
  addTip:{
    type:String,
    default:""
  },
  delTip:{
    type:String,
    default:""
  }
})

const areaList = ref([]) // 地区列表
const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const procInquiryComplianceForPatientAgeGe = ref() // 列表的数据
const procInquiryComplianceForPatientAgeLt = ref() // 列表的数据
const procInquiryCompliance = ref() // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  optionType:props.optionType
})
const allConfigForm = ref({
  procInquiryCompliance:true,//问诊合规设置是否开启
  procInquiryComplianceForPatientAgeGe:null, // 大于等于
  procInquiryComplianceForPatientAgeLt:null, // 小于
})
const rulesForm = ref({
  id:null,
  procInquiryComplianceAllowForPregnancyLactation:undefined,
  procInquiryComplianceForPatientAgeGe:null, // 大于等于
  procInquiryComplianceForPatientAgeLt:null, // 小于
  areas:[],
})
const configDialogTitle = ref('新增规则')
const rulesDialogVisible = ref(false)

const allConfigVisible = ref(false)
const  selectItem = ref({}) ;
const getList = async () => {
  loading.value = true
  try {
    const data = await SettingApi.pageConfigArea(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const allConfigSubmit = async () => {
  if(allConfigForm.value.procInquiryComplianceForPatientAgeGe || allConfigForm.value.procInquiryComplianceForPatientAgeLt){
    await globalConfigSave(allConfigForm.value);
    procInquiryComplianceForPatientAgeGe.value = allConfigForm.value.procInquiryComplianceForPatientAgeGe
    procInquiryComplianceForPatientAgeLt.value = allConfigForm.value.procInquiryComplianceForPatientAgeLt
    message.success('保存成功')
    allConfigVisible.value = false
    return;
  }
  message.error("患者年龄限制不可为空")
}

const allConfigCancel = () =>{
  allConfigForm.value.procInquiryCompliance = procInquiryCompliance.value
  allConfigForm.value.procInquiryComplianceForPatientAgeGe = procInquiryComplianceForPatientAgeGe.value
  allConfigForm.value.procInquiryComplianceForPatientAgeLt = procInquiryComplianceForPatientAgeLt.value
  allConfigVisible.value = false
}

const rulesSubmitFn = async() => {
  if(!rulesForm.value.areas || rulesForm.value.areas.length == 0) {
    message.warning('请选择地区')
    return;
  }
  if( rulesForm.value.procInquiryComplianceAllowForPregnancyLactation != null
    || rulesForm.value.procInquiryComplianceForPatientAgeGe
    || rulesForm.value.procInquiryComplianceForPatientAgeLt) {
    let param = {
      optionType:props.optionType,
      procInquiryCompliance:true,
      area:rulesForm.value.areas[rulesForm.value.areas.length-1],
      ...rulesForm.value,
    }
    await SettingApi.saveConfigArea(param)
    message.success('操作成功')
    rulesDialogVisible.value = false
    rulesCancelFn()
    await getList()
    return
  }
  message.warning('请填写限制规则')
}
const rulesCancelFn = () => {
  rulesForm.value.procInquiryComplianceForPatientAgeGe = null
  rulesForm.value.procInquiryComplianceForPatientAgeLt = null
  rulesForm.value.procInquiryComplianceAllowForPregnancyLactation = null
  rulesForm.value.areas = []
  rulesDialogVisible.value = false

}
const handelDelete = async (row) => {
  selectItem.value = Object.assign({}, row)
  await SettingApi.deleteAreaConfig({ids:[selectItem.value.id]})
  message.success('删除成功')
  await getList()
}

const handelEdit = async (row) => {
  let opeRow = cloneDeep(Object.assign({}, row));
  rulesForm.value.procInquiryComplianceForPatientAgeGe = opeRow.procInquiryComplianceForPatientAgeGe
  rulesForm.value.procInquiryComplianceForPatientAgeLt = opeRow.procInquiryComplianceForPatientAgeLt
  rulesForm.value.procInquiryComplianceAllowForPregnancyLactation = opeRow.procInquiryComplianceAllowForPregnancyLactation
  rulesForm.value.id = opeRow.id
  // 处理地区回显
  rulesForm.value.areas = treeToIds(areaList.value,row.areaCode ? row.areaCode : row.cityCode);

  configDialogTitle.value = '编辑规则'
  rulesDialogVisible.value = true
}

const getGlobalConfig = async () =>{
  let config = await globalConfigQuery();
  allConfigForm.value.procInquiryCompliance = config.procInquiryCompliance
  allConfigForm.value.procInquiryComplianceForPatientAgeGe = config.procInquiryComplianceForPatientAgeGe
  allConfigForm.value.procInquiryComplianceForPatientAgeLt = config.procInquiryComplianceForPatientAgeLt

  procInquiryCompliance.value = config.procInquiryCompliance
  procInquiryComplianceForPatientAgeGe.value = config.procInquiryComplianceForPatientAgeGe
  procInquiryComplianceForPatientAgeLt.value = config.procInquiryComplianceForPatientAgeLt
}

watch(
  () => props.active,
  async (val: any[]) => {
    if (val && val[val.length-1] == props.optionType) {
      areaList.value = await AreaApi.getAreaTree()
      await getGlobalConfig()
      await getList()
    }
  }
)
</script>
<style lang="scss" scoped>
.header_btn {
  display: flex;
  justify-content: space-between;
}
.align_center {
  display: flex;
  margin-bottom: 10px;
}
.limit_cla {
  width: 50%;
  .label {
    width: 100px;
  }
}
.header_btn_left {
  display: flex;
  align-items: center;
  font-size: 16px;
}
</style>
