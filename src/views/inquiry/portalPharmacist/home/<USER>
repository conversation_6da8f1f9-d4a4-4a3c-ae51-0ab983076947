<template>
  <div class="home-page-container">
    <div class="pharmacist-start-work" v-if="viewModel === 1">
      <div class="banner-view">
        <img src="@/assets/pharmacist/index.png" />
      </div>
      <template v-if="state.onlineStatus === 0">
        <div class="online-status-box">
          <p class="status-text">您当前还未打卡上班，点击下方按钮可后查看今日待办工作~</p>
          <el-button size="large" type="primary" @click="startReceipt">审方</el-button>
        </div>
      </template>
      <template v-else-if="state.onlineStatus === 1">
        <div class="online-status-box">
          <p class="status-text"
            >当前还有<span class="highline">{{ state.numAwaitCheck }}</span
            >个处方待审核</p
          >
          <el-button
            size="large"
            :type="state.numAwaitCheck > 0 ? 'primary' : 'info'"
            :disabled="!(state.numAwaitCheck > 0)"
            @click="handleDoCheck"
            >查看处方</el-button
          >
        </div>
      </template>
    </div>
    <template v-else-if="viewModel === 2">
      <ReviewCase ref="refReviewCase" @close="emitReviewCaseClose" />
    </template>
  </div>

  <!-- 上下班打卡组件 -->
  <WorkClockDialog ref="refWorkClockDialog" @success="emitSuccessWorkClockDialog" />

  <!-- 录入指纹组件 -->
  <AddUpdateFinger ref="refAddUpdateFinger" @success="emitSuccessAddUpdateFinger" />
</template>
<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { InquiryPortalPharmacistApi } from '@/api/inquiry/portalPharmacist/index'
import { CACHE_KEY, useCache, deleteUserCache } from '@/hooks/web/useCache'
import WorkClockDialog from '@/views/inquiry/portalPharmacist/components/WorkClockDialog.vue'
import AddUpdateFinger from '@/views/inquiry/portalPharmacist/components/AddUpFinger.vue'
import ReviewCase from '../review/index.vue'
import { ElLoading, ElNotification } from 'element-plus'

defineOptions({ name: 'PortalPharmacistHome' })

const { proxy } = getCurrentInstance()
const userStore = useUserStore()
const message = useMessage()
const { wsCache } = useCache()

const viewModel = ref(1) // 视图模式，1:工作台；2:审方详情

const state = reactive({
  preOnlineStatus: -1, // 药师出诊状态，0停诊；1出诊
  onlineStatus: -1, // 药师出诊状态，0停诊；1出诊
  hasFingerPrint: false, // 是否有指纹信息，true有，fals无
  numAwaitCheck: 0 // 待审核数量
})

// 定时器
const loopable = ref(true)
const loopTimer = ref(null)
const clearLoopTimer = () => {
  clearTimeout(loopTimer.value)
  loopTimer.value = null
}

const refReviewCase = ref(null)

// 查询药师出诊状态
const queryReceiptStatus = async () => {
  try {
    const respData = await InquiryPortalPharmacistApi.queryPharmacistReceipt({
      userId: userStore.getUser.id
    })
    state.onlineStatus = respData.onlineStatus
    if (state.preOnlineStatus == -1) {
      state.preOnlineStatus = respData.onlineStatus
    } else {
      if (state.onlineStatus != state.preOnlineStatus) {
        if (refReviewCase.value) {
          viewModel.value = 1
        }
      }
      state.preOnlineStatus = respData.onlineStatus
    }
    state.hasFingerPrint = respData.hasFingerPrint
    if (refReviewCase.value) {
      refReviewCase.value.setFingerPrintAudit(respData.fingerPrintAudit)
    }
    // ==1，出诊状态，查询待审方数量
    if (state.onlineStatus == 1) {
      loopable.value = true
      fetchAuditCount()
    } else {
      loopable.value = false
      clearLoopTimer()
    }
  } catch (e) {
    console.error(e)
  }
}
queryReceiptStatus()

// 查询待审核处方数量
const fetchAuditCount = async () => {
  try {
    const respData = await InquiryPortalPharmacistApi.getAuditCount()
    state.numAwaitCheck = respData || 0
  } catch (e) {
    console.error(e)
    if(e.code == 2005000002) {
      // 药师停诊
      ElNotification.info({ title: "提示", message: e.msg })
      viewModel.value = 1
      state.onlineStatus = 0

      userStore.setPharmacistRefresh(true)

      loopable.value = false
      clearLoopTimer()
    }
  } finally {
    // 开启定时器
    if (loopable.value) {
      loopTimer.value = setTimeout(() => {
        fetchAuditCount()
      }, 2000)
    }
  }
}

// 出诊
const refWorkClockDialog = ref(null)
const refAddUpdateFinger = ref(null)
const startReceipt = async () => {
  // 获取用户配置信息
  const userStoreInfo = await InquiryPortalPharmacistApi.getUserInfo()
  // needClockIn,是否需要打卡 (0是 1否)
  if (userStoreInfo && userStoreInfo.needClockIn === 0) {
    if (state.hasFingerPrint) {
      // 有指纹，去验证指纹
      refWorkClockDialog.value.open({ title: '上班打卡' })
    } else {
      // 无指纹，去录入指纹
      refAddUpdateFinger.value.open({ title: '录入指纹' })
    }
  } else {
    // 不需要打卡
    doStartReceipt()
  }
}
// 指纹校验成功回调
const emitSuccessWorkClockDialog = ({ userId, tenantId, fingerprintInfo }) => {
  doStartReceipt()
}
// 录入指纹成功回调
const emitSuccessAddUpdateFinger = async ({ userId, tenantId, fingerprintInfo }) => {
  // 重置标识
  state.hasFingerPrint = true
  // 出诊
  doStartReceipt()
}
// 出诊请求
const doStartReceipt = async () => {
  try {
    await InquiryPortalPharmacistApi.startPharmacistReceipt()
    userStore.setPharmacistRefresh(true)
  } catch (e) {
    console.error(e)
  }
}
watch(
  () => userStore.getPharmacistRefresh,
  (newValue) => {
    if (newValue) {
      queryReceiptStatus()
      setTimeout(() => {
        userStore.setPharmacistRefresh(false)
      }, 0)
    }
  }
)

// 去审方
const handleDoCheck = () => {
  loopable.value = false
  clearLoopTimer()
  const loadingInstance = ElLoading.service({
    // fullscreen: true,
    target: document.querySelector('.home-page-container'),
    text: '处方加载中...'
  })
  setTimeout(() => {
    loadingInstance.close()
    viewModel.value = 2
  }, 1000)
}

// 审方组件关闭回调
const emitReviewCaseClose = () => {
  viewModel.value = 1
  userStore.setPharmacistRefresh(true)
}

onUnmounted(() => {
  loopable.value = false
  clearLoopTimer()
})
</script>

<style lang="scss" scoped>
.home-page-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  position: absolute;
  top: 0;
  left: 0;

  .pharmacist-start-work {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .banner-view {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .online-status-box {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .status-text {
        margin: 40px 0;
        color: #222222;
        font-size: 18px;

        .highline {
          color: #ff1b1b;
          font-weight: 600;
        }
      }
    }
  }
}

::v-deep {
  .el-button {
    padding: 10px 30px;
    box-sizing: border-box;
  }
}
</style>
