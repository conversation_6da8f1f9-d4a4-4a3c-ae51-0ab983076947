<template>
  <ContentWrap>
    <div class="action-wrap p-6px flex justify-between items-stretch">
      <!-- 新建商品 -->
      <div class="action-left flex flex-col">
        <span class="action-head leading-none">新建商品</span>
        <div class="action-content mt-16px flex justify-start items-center">
          <button
            class="action-button"
            @click="handleNg2RouterLink('/goods/product/product-add-single')"
          >
            <img class="action-button-image" src="@/assets/inquiry/product/icon-add.png" />
            <div class="action-button-text">
              <span class="strong">单个新增</span>
              <span>适合创建单个商品</span>
            </div>
          </button>
          <button class="action-button" @click="handleNg2RouterLink('')">
            <img class="action-button-image" src="@/assets/inquiry/product/icon-add-plus.png" />
            <div class="action-button-text">
              <span class="strong">批量新增</span>
              <span>适合创建大量商品</span>
            </div>
          </button>
          <button
            class="action-button"
            @click="handleNg2RouterLink('/goods/product/productStoreForm')"
          >
            <img class="action-button-image" src="@/assets/inquiry/product/icon-store.png" />
            <div class="action-button-text">
              <span class="strong">门店商品(5)</span>
              <span>门店创建的商品</span>
            </div>
          </button>
        </div>
      </div>
      <!-- 其他操作 -->
      <div class="action-right flex flex-col">
        <span class="action-head leading-none">其他操作</span>
        <div class="link-content mt-16px flex flex-wrap justify-start items-start">
          <template v-for="linkItem in computedQuickLinks.hotLink" :key="linkItem.name">
            <div class="link-action-box">
              <span class="action-link" @click="handleNg2RouterLink(linkItem.url)">{{
                linkItem.name
              }}</span>
            </div>
          </template>
          <el-dropdown
            trigger="hover"
            class="link-action-box"
            popper-class="link-action-box-popper"
          >
            <div class="action-link"
              >全部<el-icon>
                <DArrowRight size="14" />
              </el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <template v-for="category in computedQuickLinks.categoryLink" :key="category.name">
                  <li class="el-dropdown-item-head-title">{{ category.name }}</li>
                  <template v-for="linkItem in category.list" :key="linkItem.name">
                    <el-dropdown-item @click="handleNg2RouterLink(linkItem.url)">
                      <span>{{ linkItem.name }}</span>
                    </el-dropdown-item>
                  </template>
                </template>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>
  </ContentWrap>

  <ContentWrap>
    <el-form
      class="query-form"
      ref="refQueryForm"
      :model="queryForm"
      :inline="true"
      label-width="110px"
      :style="{ overflow: 'hidden', height: isExpandQueryForm ? 'auto' : '44px' }"
    >
      <!-- 商品信息 -->
      <el-form-item label="商品信息" prop="mixedQuery">
        <!-- (110+280)*2+12=792 -->
        <el-input
          v-model.trim="queryForm.mixedQuery"
          :placeholder="computedPrefInputConfig.placeholder"
          :maxlength="computedPrefInputConfig.maxlength"
          clearable
          class="!w-682px"
        >
          <template #prepend>
            <el-select
              v-model="queryForm.mixedQueryType"
              placeholder="关键字"
              clearable
              class="!w-140px"
            >
              <el-option label="多个条形码" value="1" />
              <el-option label="多个商品编码" value="2" />
              <el-option label="多个标准库ID" value="3" />
              <el-option label="单个通用名称" value="4" />
              <el-option label="单个品牌名称" value="5" />
            </el-select>
          </template>
        </el-input>
      </el-form-item>

      <!-- 商品分类 -->
      <el-form-item label="商品分类" prop="productCategory">
        <el-select
          v-model="queryForm.productCategory"
          placeholder="请选择商品分类"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_PRODUCT_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 所属范围 -->
      <el-form-item label="所属范围" prop="businessScope">
        <el-select
          v-model="queryForm.businessScope"
          placeholder="请选择所属范围"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_BUSINESS_SCOPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 处方分类 -->
      <el-form-item label="处方分类" prop="presCategory">
        <el-select
          v-model="queryForm.presCategory"
          placeholder="请选择处方分类"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_PRES_CATEGORY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 含特殊药品复方制剂 -->
      <el-form-item label="含特殊药品复方制剂" prop="hasSpecialDrugCompound">
        <el-select
          v-model="queryForm.hasSpecialDrugCompound"
          placeholder="请选择含特殊药品复方制剂"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 是否特价 -->
      <el-form-item label="是否特价" prop="specialPrice">
        <el-select
          v-model="queryForm.specialPrice"
          placeholder="请选择是否特价"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 积分商品 -->
      <el-form-item label="积分商品" prop="integral">
        <el-select
          v-model="queryForm.integral"
          placeholder="请选择是否积分商品"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 标签 -->
      <el-form-item label="标签" prop="remark">
        <el-select v-model="queryForm.remark" placeholder="请选择标签" clearable class="!w-280px">
          <el-option label="A类" value="1" />
          <el-option label="B类" value="2" />
          <el-option label="C类" value="3" />
          <el-option label="D类" value="4" />
          <el-option label="E类" value="5" />
        </el-select>
      </el-form-item>

      <!-- 医保匹配状态 -->
      <el-form-item label="医保匹配状态" prop="medicareMatchStatus">
        <el-select
          v-model="queryForm.medicareMatchStatus"
          placeholder="请选择医保匹配状态"
          clearable
          class="!w-280px"
        >
          <el-option label="已匹配" value="1" />
          <el-option label="未匹配" value="0" />
        </el-select>
      </el-form-item>

      <!-- 医保项目编码 -->
      <el-form-item label="医保项目编码" prop="medicareProjectCode">
        <el-input
          v-model="queryForm.medicareProjectCode"
          placeholder="请输入医保项目编码"
          clearable
          class="!w-280px"
          maxlength="50"
        />
      </el-form-item>

      <!-- 生产厂家 -->
      <el-form-item label="生产厂家" prop="manufacturer">
        <el-input
          v-model="queryForm.manufacturer"
          placeholder="请输入生产厂家"
          clearable
          class="!w-280px"
          maxlength="50"
        />
      </el-form-item>

      <!-- 是否有标识码 -->
      <el-form-item label="是否有标识码" prop="drugIdentCode">
        <el-select
          v-model="queryForm.drugIdentCode"
          placeholder="请选择是否有标识码"
          clearable
          class="!w-280px"
        >
          <el-option label="有标识码" value="1" />
          <el-option label="无标识码" value="0" />
        </el-select>
      </el-form-item>

      <!-- 存储条件 -->
      <el-form-item label="存储条件" prop="storageWay">
        <el-select
          v-model="queryForm.storageWay"
          placeholder="请选择存储条件"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_STORAGE_WAY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 资质是否过期 -->
      <el-form-item label="资质是否过期" prop="productValidity">
        <el-select
          v-model="queryForm.productValidity"
          placeholder="请选择资质是否过期"
          clearable
          class="!w-280px"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>

      <!-- 停售状态 -->
      <el-form-item label="停售状态" prop="stopSale">
        <el-select
          v-model="queryForm.stopSale"
          placeholder="请选择停售状态"
          clearable
          class="!w-280px"
        >
          <el-option label="未停售" value="0" />
          <el-option label="已停售" value="1" />
        </el-select>
      </el-form-item>

      <!-- 禁采状态 -->
      <el-form-item label="禁采状态" prop="purchaseDisabled">
        <el-select
          v-model="queryForm.purchaseDisabled"
          placeholder="请选择禁采状态"
          clearable
          class="!w-280px"
        >
          <el-option label="未禁采" value="0" />
          <el-option label="总部禁采" value="1" />
          <el-option label="总部&门店禁采" value="2" />
        </el-select>
      </el-form-item>

      <!-- 审批状态 -->
      <el-form-item label="审批状态" prop="approvalStatus">
        <el-select
          v-model="queryForm.approvalStatus"
          placeholder="请选择审批状态"
          clearable
          class="!w-280px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.PRODUCT_BPM_APPROVAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <!-- 医保限制 -->
      <el-form-item label="医保限制" prop="medicareLimit">
        <el-select
          v-model="queryForm.medicareLimit"
          placeholder="请选择医保限制"
          clearable
          class="!w-280px"
        >
          <el-option label="有限制" value="1" />
          <el-option label="无限制" value="0" />
        </el-select>
      </el-form-item>

      <!-- 创建人 -->
      <el-form-item label="创建人" prop="createBy">
        <el-input
          v-model="queryForm.createBy"
          placeholder="请输入创建人"
          clearable
          class="!w-280px"
          maxlength="50"
        />
      </el-form-item>

      <!-- 创建时间 -->
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker
          v-model="queryForm.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-280px"
        />
      </el-form-item>
    </el-form>

    <div class="action-form w-100% pl-110px flex justify-start items-stretch">
      <el-button type="primary" @click="handleQuery">查询</el-button>
      <el-button type="default" @click="resetQuery">重置</el-button>
      <div
        class="action-from-expand ml-12px cursor-pointer select-none flex justify-start items-center"
        @click="isExpandQueryForm = !isExpandQueryForm"
      >
        <span class="action-txt">{{ isExpandQueryForm ? '收起' : '展开' }}</span>
        <el-icon size="14"><component :is="isExpandQueryForm ? ArrowUp : ArrowDown" /></el-icon>
      </div>
    </div>

    <!-- 新建商品按钮组 -->
    <!--
    <el-row :gutter="10" class="mb-10">
      <el-col :span="1.5">
        <el-button
          type="primary"
          @click="openForm('create')"
          v-hasPermi="['saas:product:info:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增单个商品
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" @click="handleExport" v-hasPermi="['saas:product:info:import']">
          <Icon icon="ep:upload" class="mr-5px" /> Excel批量导入
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          v-hasPermi="['saas:product:info:import']"
        >
          <Icon icon="ep:shop" class="mr-5px" /> 门店商品导入
        </el-button>
      </el-col>
      <el-col :span="1.5" v-if="getIsChainHeadOrSingleStore">
        <router-link :to="'/goods/product/unbundled-info'">
          <el-button link type="primary" v-hasPermi="['saas:product:unbundled:query']">
            <Icon icon="ep:box" class="mr-5px" /> 拆零商品维护
          </el-button>
        </router-link>
      </el-col>
    </el-row>
    -->
  </ContentWrap>

  <ContentWrap>
    <!-- 列表按钮 -->
    <div class="table-header-action w-100% mb-10px flex justify-between items-center">
      <div class="header-left">&nbsp;</div>
      <div class="header-right">
        <el-button type="primary">筛选列</el-button>
        <el-button type="primary" @click="handleExport">下载</el-button>
        <el-dropdown
          class="el-dropdown-link ml-12px"
          trigger="hover"
          @visible-change="onTableMoreActionVisibleChange"
          @command="onTableMoreActionCommand"
        >
          <el-button type="default">
            <div class="dropdown-link-text flex justify-center items-center">
              更多操作<el-icon size="14">
                <component :is="isExpandTableMoreAction ? ArrowUp : ArrowDown" />
              </el-icon>
            </div>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="批量停售">批量停售</el-dropdown-item>
              <el-dropdown-item command="批量解停">批量解停</el-dropdown-item>
              <el-dropdown-item command="批量删除">批量删除</el-dropdown-item>
              <el-dropdown-item command="批量禁采">批量禁采</el-dropdown-item>
              <el-dropdown-item command="批量取消禁采">批量取消禁采</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <!-- 列表 -->
    <!-- show-overflow-tooltip -->
    <el-table
      v-loading="queryTable.loading"
      stripe
      highlight-current-row
      header-row-class-name="header-row-class-name"
      row-class-name="row-class-name"
      :data="queryTable.list"
    >
      <el-table-column type="selection" width="40" align="left" fixed="left" />
      <el-table-column type="index" label="序号" width="60" align="left" fixed="left" />
      <el-table-column label="商品信息" width="400" align="left">
        <template #default="{ row }">
          <div class="item-product-info-cell">
            <el-image
              class="cell-image"
              :src="
                row.coverImages?.[0] ||
                'https://oss-ec-test.ybm100.com//ybm/product/min/defaultPhoto.jpg'
              "
              fit="contain"
            />
            <div class="cell-context">
              <div class="item-cxt">
                <div class="item-cxt-txt"
                  >【{{ row.brandName }}】{{ row.commonName }}{{ row.spec }}{{ row.unit }}</div
                >
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">条形码：{{ row.barcode }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">批准文号：{{ row.approvalNumber }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">生产厂家：{{ row.manufacturer }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">标准库ID：{{ row.stdlibId }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">商品编码：{{ row.pref }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="医保信息" width="300" align="left">
        <template #default="{ row }">
          <div class="item-product-info-cell">
            <div class="cell-context">
              <div class="item-cxt">
                <div class="item-cxt-txt">医保项目名称：{{ row.useInfo?.medicareProjectName }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">医保项目编码：{{ row.useInfo?.medicareProjectCode }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt"
                  >医保项目等级：{{ row.useInfo?.medicareProjectLevel }}</div
                >
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
              <div class="item-cxt">
                <div class="item-cxt-txt">最小包装数量：{{ row.unbundledQuantity }}</div>
                <el-icon class="item-cxt-copy" :size="14" color="#00b955"><DocumentCopy /></el-icon>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品分类" prop="firstCategory" width="90" align="left" />
      <el-table-column label="所属范围" prop="businessScope" width="90" align="left">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_BUSINESS_SCOPE" :value="scope.row.businessScope" />
        </template>
      </el-table-column>
      <el-table-column label="处方分类" prop="presCategory" width="90" align="left">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_PRES_CATEGORY" :value="scope.row.presCategory" />
        </template>
      </el-table-column>
      <el-table-column label="零售价" prop="retailPrice" width="90" align="left" />
      <el-table-column label="会员价" prop="memberPrice" width="90" align="left" />
      <el-table-column label="审批状态" prop="status" width="90" align="left">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="剂型" prop="dosageForm" width="90" align="left">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_DOSAGE_FORM" :value="scope.row.dosageForm" />
        </template>
      </el-table-column>
      <el-table-column label="有效期" prop="productValidity" width="90" align="left" />
      <el-table-column label="产地" prop="origin" width="90" align="left" />
      <el-table-column
        label="批准文号有效期"
        prop="approvalValidityPeriod"
        width="140"
        align="left"
      />
      <el-table-column label="标签" prop="remark" width="90" align="left" />
      <el-table-column label="特价商品" prop="remark" width="90" align="left" />
      <el-table-column label="积分商品" prop="remark" width="90" align="left" />
      <el-table-column label="储存条件" prop="storageWay" width="90" align="left">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.PRODUCT_STORAGE_WAY" :value="scope.row.storageWay" />
        </template>
      </el-table-column>
      <el-table-column label="养护类型" prop="remark" width="90" align="left" />
      <el-table-column
        label="含特殊药品复方制剂"
        prop="hasSpecialDrugCompound"
        width="160"
        align="left"
      />
      <el-table-column label="资质过期" prop="remark" width="90" align="left" />
      <el-table-column label="进项税率" prop="inputTaxRate" width="90" align="left" />
      <el-table-column label="销项税率" prop="outputTaxRate" width="90" align="left" />
      <el-table-column label="创建人" prop="createBy" width="90" align="left" />
      <el-table-column
        label="创建时间"
        width="110"
        prop="createTime"
        align="left"
        :formatter="dateFormatter"
      />
      <el-table-column label="药品标识码" prop="drugIdentCode" width="100" align="left" />
      <el-table-column label="医保匹配状态" prop="drugIdentCode" width="110" align="left" />
      <el-table-column label="医保限制" prop="drugIdentCode" width="90" align="left" />
      <el-table-column label="停售状态" prop="drugIdentCode" width="90" align="left" />
      <el-table-column label="禁采状态" prop="drugIdentCode" width="90" align="left" />
      <el-table-column label="操作" width="90" align="left" fixed="right">
        <template #default="scope">
          <div class="item-product-action-cell">
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['saas:product:info:update']"
              >编辑</el-button
            >
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['saas:product:info:update']"
              >禁采</el-button
            >
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['saas:product:info:update']"
              >停售
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row)"
              v-hasPermi="['saas:product:info:delete']"
              >删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-model:page="queryTable.pageNo"
      v-model:limit="queryTable.pageSize"
      :total="queryTable.total"
      @pagination="queryDataList"
    />
  </ContentWrap>

  <!-- Dialog 组件 -->
  <el-dialog title="选择删除范围" v-model="dialogVisible" width="30%" :before-close="cancelDelete">
    <el-radio-group v-model="deleteType">
      <el-radio :label="1">仅删除商品列表</el-radio>
      <el-radio :label="2">删除全部记录</el-radio>
    </el-radio-group>

    <template #footer>
      <el-button @click="cancelDelete">取消</el-button>
      <el-button type="primary" @click="confirmDelete">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { ArrowUp, ArrowDown, DArrowRight, DocumentCopy } from '@element-plus/icons-vue'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { ProductInfoApi, ProductInfoVO } from '@/api/inquiry/product/product'
import { ProductRecycleApi } from '@/api/inquiry/product/recycle'

import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'

defineOptions({ name: 'ProductList' })

const route = useRoute()
const router = useRouter()
const message = useMessage()

// 判断总部&门店&单店
const getIsChainHeadOrSingleStore = computed(() => {
  return true
})

// 点击跳转
const handleNg2RouterLink = async (url: string) => {
  if (url) {
    router.push({ path: url })
  }
}
// 其他操作
const computedQuickLinks = computed(() => {
  const links = [
    {
      category: 'inventory',
      name: '库存查询',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '报损报溢',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '药品养护',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '货位管理',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'inventory',
      name: '期初库存导入',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '批量修改',
      url: '',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '拆零规则',
      url: '/goods/product/unbundled-info',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '售价调整',
      url: '/goods/product/product-gsp/product-price-adjustment',
      hotLinkSort: -1
    },
    {
      category: 'product',
      name: '商品回收站',
      url: '/goods/product/recycle',
      hotLinkSort: -1
    }
  ]

  // 热链排序
  const hotLinksArr = ['库存查询', '批量修改', '拆零规则', '报损报溢', '货位管理']
  links.forEach((link) => {
    link.hotLinkSort = -1
    if (hotLinksArr.includes(link.name)) {
      link.hotLinkSort = hotLinksArr.indexOf(link.name)
    }
  })

  const hotLink = links
    .filter((link) => link.hotLinkSort !== -1)
    .sort((a, b) => a.hotLinkSort - b.hotLinkSort) // 热链+排序
  const inventoryLink = links.filter((link) => link.category === 'inventory') // 库存
  const productLink = links.filter((link) => link.category === 'product') // 商品

  return {
    hotLink,
    categoryLink: [
      {
        name: '库存',
        list: inventoryLink
      },
      {
        name: '商品',
        list: productLink
      }
    ]
  }
})

// 查询表单
const queryForm = reactive({
  mixedQueryType: undefined as string | undefined, // 商品信息类型
  mixedQuery: undefined, // 商品信息
  productCategory: undefined, // 商品分类
  businessScope: undefined, // 所属范围
  presCategory: undefined, // 处方分类
  hasSpecialDrugCompound: undefined as string | undefined, // 含特殊药品复方制剂
  specialPrice: undefined as string | undefined, // 是否特价
  integral: undefined as string | undefined, // 积分商品
  remark: undefined, // 标签
  medicareMatchStatus: undefined as string | undefined, // 医保匹配状态
  medicareProjectCode: undefined, // 医保项目编码
  manufacturer: undefined, // 生产厂家
  drugIdentCode: undefined as string | undefined, // 是否有标识码
  storageWay: undefined, // 存储条件
  productValidity: undefined as string | undefined, // 资质是否过期
  stopSale: undefined as string | undefined, // 停售状态
  purchaseDisabled: undefined as string | undefined, // 禁采状态
  approvalStatus: undefined, // 审批状态
  medicareLimit: undefined as string | undefined, // 医保限制
  createBy: undefined, // 创建人
  createTime: [] // 创建时间
})
// 查询table
const queryTable = reactive({
  pageNo: 1,
  pageSize: 10,
  loading: false,
  total: 0,
  list: [] as ProductInfoVO[]
})

// 商品信息输入框
const computedPrefInputConfig = computed(() => {
  let placeholder = ''
  let maxlength = 0
  switch (queryForm.mixedQueryType) {
    case '1':
      placeholder = '最多可录入50个条形码，以英文逗号隔开，最多700个字符'
      maxlength = 700
      break
    case '2':
      placeholder = '最多可录入50个商品编码，以英文逗号隔开，最多700个字符'
      maxlength = 700
      break
    case '3':
      placeholder = '最多可录入50个标准库ID，以英文逗号隔开，最多700个字符'
      maxlength = 700
      break
    case '4':
      placeholder = '仅可录入1个通用名称，支持模糊搜索，最大长度50'
      maxlength = 50
      break
    case '5':
      placeholder = '仅可录入1个品牌名称，支持模糊搜索，最大长度50'
      maxlength = 50
      break
    default:
      placeholder = '请输入商品编码/通用名称/品牌名称/条形码/助记码/批准文号/生产厂家/标准库ID'
      maxlength = 50
      break
  }
  return { placeholder, maxlength }
})

// 查询
const handleQuery = () => {
  queryTable.pageNo = 1
  queryDataList()
}
// 重置
const refQueryForm = ref<any>(null)
const resetQuery = () => {
  refQueryForm.value?.resetFields()
  queryForm.mixedQueryType = undefined
  handleQuery()
}
// 展开&收起
const isExpandQueryForm = ref(false) // 展开查询表单

// 列表查询
const queryDataList = async () => {
  try {
    queryTable.loading = true
    const respData = await ProductInfoApi.getProductInfoPage({
      pageNo: queryTable.pageNo,
      pageSize: queryTable.pageSize,
      ...queryForm
    })
    queryTable.total = respData.total || 0
    if (Array.isArray(respData.list)) {
      queryTable.list = respData.list.map((item) => {
        return {
          ...item,
          retailPrice: item.useInfo?.retailPrice,
          memberPrice: item.useInfo?.memberPrice
        }
      })
    }
  } catch (err) {
    console.log(err)
  } finally {
    queryTable.loading = false
  }
}

// table更多操作
const isExpandTableMoreAction = ref<boolean>(false) // 展开更多操作
const onTableMoreActionVisibleChange = (visible: boolean) => {
  isExpandTableMoreAction.value = visible
}
const onTableMoreActionCommand = (command: string | number | object) => {
  console.log(command)
  // 请先选择一个商品
}

const openForm = (type: string, id?: number) => {}

/** 删除按钮操作 */
const deleteType = ref(1)
const dialogVisible = ref(false)
const deleteRowId = ref<number>()
const handleDelete = async (row) => {
  try {
    // 1. 校验回收站数据量
    const data = await ProductRecycleApi.getProductPage({ pageNo: 1, pageSize: 1 })
    const recycleCount = data.total
    if (recycleCount >= 1000) {
      const confirmResult = await ElMessageBox.confirm(
        '您的商品回收站存在1000条历史删除数据，请前往商品回收站删除历史数据后，再操作',
        '提示',
        {
          confirmButtonText: '去删除',
          cancelButtonText: '取消'
        }
      )
      if (confirmResult === 'confirm') {
        // 跳转到回收站页面
        router.push({ name: 'ProductRecycleInfo' })
      }
      return
    }

    // 2. 校验是否有库存
    const hasStock = await ProductInfoApi.checkProductStock(row.id)

    // 3. 有库存时的二次确认
    if (hasStock.data) {
      const confirmResult = await ElMessageBox.confirm('该商品存在库存，是否继续删除?', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
      if (confirmResult !== 'confirm') {
        return
      }
    }

    // 4. 选择删除范围
    deleteRowId.value = row.id
    dialogVisible.value = true
  } catch (error) {
    console.error('Delete failed:', error)
    cancelDelete()
  }
}

/** 取消删除操作 */
const cancelDelete = () => {
  dialogVisible.value = false
}
/** 确认删除操作 */
const confirmDelete = async () => {
  try {
    // 发起删除
    await ProductInfoApi.deleteProduct({
      idList: [deleteRowId.value],
      deleteType: deleteType.value
    })
    message.success('操作成功')
    // 刷新列表
    await queryDataList()
  } catch {
    message.error('操作失败，请稍后重试')
  }
  dialogVisible.value = false
}

/** Excel导入操作 */
const exportLoading = ref(false) // 导出的加载中
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProductInfoApi.exportProductInfo(queryForm)
    download.excel(data, '商品基本信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  queryDataList()
})
</script>

<style lang="scss" scoped>
.action-wrap {
  .action-head {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
  }

  .action-left {
    flex-shrink: 0;

    .action-content {
      .action-button {
        width: 240px;
        height: 64px;
        padding: 12px;
        box-sizing: border-box;
        background: #ffffff;
        box-shadow: 0 5px 5px 0 #0000001a;
        border: 1px solid #cdcdcd;
        border-radius: 4px;
        margin-right: 16px;
        cursor: pointer;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        &:hover {
          border-color: #00b955;
          // background-color: #ecf5ff;
        }

        &:last-child {
          margin-right: 0;
        }

        .action-button-image {
          width: 40px;
          height: 40px;
          border-radius: 4px;
        }

        .action-button-text {
          margin-left: 12px;
          line-height: 1;
          color: #666666;
          font-size: 14px;
          font-weight: 400;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: flex-start;

          .strong {
            margin-bottom: 8px;
            line-height: 1;
            color: #000000;
            font-size: 18px;
            font-weight: 550;
          }
        }
      }
    }
  }

  .action-right {
    flex-shrink: 0;

    .link-content {
      width: 320px;
      background-color: #f5f5f5;
      padding: 12px;
      padding-bottom: 0;
      box-sizing: border-box;
      border-radius: 4px;

      .link-action-box {
        width: 33.3333%;
        margin-bottom: 12px;
        line-height: 1;
        display: inline-flex;
        justify-content: flex-start;
        align-items: center;

        .action-link {
          cursor: pointer;
          color: #000000;
          font-size: 14px;

          &:hover {
            color: #00b955;
          }

          &:focus-visible {
            outline: none;
          }
        }

        &.el-dropdown {
          ::v-deep {
            .el-tooltip__trigger {
              display: flex;
            }
          }
        }
      }
    }
  }
}
.link-action-box-popper {
  .el-dropdown-item-head-title {
    padding: 6px 8px;
    box-sizing: border-box;
    color: #000000;
    font-size: 14px;
    font-weight: 600;
  }
}

.query-form {
  .el-form-item {
    margin: 0 12px 12px 0;

    ::v-deep(.el-form-item__label) {
      line-height: 1;
      align-items: center;
      text-align: right;
      word-break: break-all;
    }
  }
}
.action-form {
  .action-from-expand {
    &:hover {
      color: #00b955;
    }

    .action-txt {
      font-size: var(--el-font-size-base);
    }
  }
}

.table-header-action {
  .header-left {
    flex-shrink: 0;
  }

  .header-right {
    flex-shrink: 0;
    .el-dropdown-link {
      .el-button {
        outline: none;

        .dropdown-link-text {
          line-height: 1;
          color: #000000;
          font-size: 14px;
        }
      }
    }
  }
}

.el-table {
  ::v-deep {
    .header-row-class-name {
      .el-table__cell {
        background-color: #eff1f5;
        .cell {
          color: #222222;
          font-size: 14px;
        }
      }
    }

    .row-class-name {
      .cell {
        color: #222222;
        font-size: 14px;
      }
    }
  }

  .item-product-info-cell {
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;

    .cell-image {
      flex-shrink: 0;
      width: 64px;
      height: 64px;
    }

    .cell-context {
      flex-grow: 1;
      margin-left: 10px;
      overflow: hidden;

      .item-cxt {
        width: 100%;
        overflow: hidden;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .item-cxt-txt {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .item-cxt-copy {
          flex-shrink: 0;
          margin-left: 2px;
          cursor: pointer;
          display: none;
        }

        &:hover {
          color: #00b955 !important;
          font-weight: 550;

          .item-cxt-copy {
            display: inline-block;
          }
        }
      }
    }
  }

  .item-product-action-cell {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;

    .el-button + .el-button {
      margin-left: 0px;
    }
  }
}
</style>
