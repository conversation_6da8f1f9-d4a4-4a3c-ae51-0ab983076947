<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :close-on-click-modal="false" width="70%">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :disabled="formDisabled"
      label-width="120px"
      v-loading="formLoading"
    >
      <el-row>
         <el-col>
             <el-form ref="formRef2">
               <div style="text-align: right;margin-right: 20px;margin-bottom: 30px">
                 <el-button
                   type="success"
                   plain
                   @click="openAuditForm(formData.id)"
                   v-if="formType === 'audit' && formData.auditStatus === 0"
                   v-hasPermi="['hospital:inquiry-hospital:update']"
                 >
                   医生审核
                 </el-button>
               </div>
             </el-form>
         </el-col>
      </el-row>

      <el-divider content-position="center">基本信息</el-divider>
      <el-row>
        <el-col :span="8">
          <el-form ref="formRef1"  :rules="formRulesMobile" :model="formData" >
            <el-form-item label="手机号" prop="mobile">
              <UserSelect :disabled="formType !== 'create'" v-model:modelValue="formData.mobile" :role-code="SystemRoleCodeEnum.DOCTOR"  @success="args =>  userInfo(args)"  @error="formData.mobile = ''" />
            </el-form-item>
          </el-form>
        </el-col>
        <el-col :span="8">
          <el-form-item label="姓名" prop="name" >
            <el-input :disabled="userData.nickname || formType !== 'create'" v-model="formData.name" maxlength="20" placeholder="请输入医生名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证号码" prop="idCard" >
            <el-input :disabled="userData.idCard || formType !== 'create'" maxlength="18" v-model="formData.idCard" placeholder="请输入身份证号码"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="性别" prop="sex" >
            <el-radio-group :disabled="userData.sex || formType !== 'create'" v-model="formData.sex">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="执业机构" prop="firstPracticeName"
            >
            <el-input :maxlength="30" :max="30" v-model="formData.firstPracticeName" placeholder="请输入第一执业机构名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="机构等级" prop="firstPracticeLevel"
            >
            <el-select
              v-model="formData.firstPracticeLevel"
              placeholder="请选择第一执业机构等级"
              clearable
              class="!w-240px"

            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.HOSPITAL_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="医生类型" prop="jobType" >
            <el-radio-group v-model="formData.jobType">
              <el-radio
                v-for="dict in getIntDictOptions(DICT_TYPE.DOCTOR_JOB_TYPE)"
                :key="dict.value"
                :label="dict.value"

              >
                {{ dict.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="专业职称" prop="titleCode" >
            <el-select
              v-model="formData.titleCode"
              placeholder="请选择专业职称"
              clearable

              class="!w-240px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.DOCTOR_TITLE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="科室" prop="firstPracticeDeptName" >
            <el-input :maxlength="64" :max="64" v-model="formData.firstPracticeDeptName" placeholder="请输入第一职业机构科室"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="渠道" prop="canal" >
            <el-select
              v-model="formData.canal"
              placeholder="请选择渠道"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.DOCTOR_CANAL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="邀请人姓名" prop="inviterName" >
            <el-input :maxlength="20"  v-model="formData.inviterName" placeholder="请输入邀请人姓名"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="邀请人工号" prop="inviterNo" >
            <el-input :maxlength="30" :max="30" v-model="formData.inviterNo" placeholder="支持手机号/工号"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label="自动抢单" prop="autoGrabStatus" >
            <el-switch v-model="formData.autoGrabStatus" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合作状态" prop="cooperation" >
            <el-select
              v-model="formData.cooperation"
              placeholder="请选择合作状态"
              clearable

              class="!w-240px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.DOCTOR_COOPERATION_STATUS)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
        <el-form-item label="环境标识" prop="envTag">
          <el-select
            v-model="formData.envTag"
            placeholder="请选择环境标识"
            clearable
            class="!w-240px"
          >
            <el-option
              v-for="item in getIntDictOptions(DICT_TYPE.SYSTEM_ENV_TAG)"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="医保编码">
            <el-input
              v-model="formData.doctorMedicareNo" :maxlength="13"
              placeholder="请输入D开头的医保编码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="擅长专业" prop="professionalDec">
            <el-input
              v-model="formData.professionalDec" :maxlength="255"
              placeholder="请输入医生擅长领域专业内容" type="textarea"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>

        <el-col :span="24">
          <el-form-item label="个人简介" prop="biography">
            <el-input
              v-model="formData.biography" :maxlength="255"
              placeholder="请输入医生个人简介信息" type="textarea"/>
          </el-form-item>
        </el-col>
      </el-row>


      <el-divider content-position="center" style="margin-top: 50px;margin-bottom: 20px">
        关联医院信息
      </el-divider>
      <div style="text-align: right;margin-right: 20px;margin-bottom: 30px">
        <el-button
          v-if="!formDisabled"
          type="success"
          @click="addHospitalRel"
          v-hasPermi="['hospital:inquiry-hospital:update']"
        >
          新增关联医院
        </el-button>
      </div>

      <div v-for="(item,index) in hospitalDoctorList" :key="item">
      <el-row style="margin-top: 20px">
        <el-col :span="7" >
          <el-form-item label="医院" :rules="[
                 { required: true, message: '互联网医院必选', trigger: ['change','blur'] }
              ]">
            <el-select
              v-model="item.hospitalPref"
              placeholder="请选择互联网医院"
              clearable
              class="!w-240px"
              :onchange="getDeptList(item)"
            >
              <el-option
                v-for="hospital in hospitalList"
                :key="hospital.id"
                :label="hospital.name"
                :value="hospital.pref"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7" >
          <el-form-item label="医生编码" >
          <el-input :maxlength="30" :max="30" v-model="item.doctorHospitalPref" placeholder="请输入医生编码"/>
          </el-form-item>
        </el-col>

        <el-col :span="7" >
<!--          <el-form-item label="科室" >
            <el-cascader v-model="item.hospitalDeptRelationId" :disabled="item.hospitalPref == undefined" :options="item.deptList" :props="deptProps" collapse-tags clearable />
          </el-form-item>-->
          <el-form-item
            label="科室"
            :rules="[
                 { required: true, message: '请选择医生在此医院的科室', trigger: ['change','blur'] }
              ]">
            <el-cascader v-model="item.hospitalDeptRelationId" :disabled="item.hospitalPref == undefined" @change="deptChange(item)" :options="item.deptList" :props="deptStrictlyProps" collapse-tags clearable />
          </el-form-item>
        </el-col>

        <el-col :span="2" >
          <el-button type="danger" @click="delHospitalRel(index)" style="margin-left: 50px;width: 30px;height: 30px" :icon="Delete" circle />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12" >
          <el-form-item label="真人开方" prop="inquiryWayType" :rules="item.inquiryWayType.length <= 0 && item.autoInquiryWayType.length <= 0 ? [
                 { required: true, message: '请至少选择一种在该医院的接诊方式', trigger: ['change','blur'] }
              ] : []">
            <el-checkbox-group v-model="item.inquiryWayType">
              <el-checkbox
                v-for="dict in getIntDictOptions(DICT_TYPE.INQUIRY_WAY_TYPE)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="自动开方" prop="autoInquiryWayType" :rules="item.inquiryWayType.length <= 0 && item.autoInquiryWayType.length <= 0 ? [
                 { required: true, message: '请至少选择一种在该医院的接诊方式', trigger: ['change','blur'] }
              ] : []">
            <el-checkbox-group v-model="item.autoInquiryWayType" @change="autoCheckChange(item)">
              <el-checkbox
                v-for="dict in getIntDictOptions(DICT_TYPE.INQUIRY_WAY_TYPE)"
                :key="dict.value"
                :label="dict.value"
              >
                {{ dict.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12" >

          <el-form-item style="width: auto" label="自动开方时段：" v-if="item.autoInquiryWayType.length > 0"
                        prop="autoInquiryTime">
            <span v-if="item.timeList.length < 1">(不配置默认全天接诊 00:00 - 24:00 )&nbsp;&nbsp;</span>
            <el-tooltip
              content="医生仅在指定时段内接诊"
              raw-content
            >
            <el-button v-if="item.timeList.length < 5" type="success" @click="addAutoInquiryTime(item)" style="width: 20px;height: 20px" :icon="Plus"
                       circle/>
            </el-tooltip>

          </el-form-item>

          <div v-if="item.autoInquiryWayType.length > 0">
            <el-row style="margin-top: 10px;align-items: center" v-for="(tm,index) in item.timeList" :key="tm">
              <el-time-select
                v-model="tm.startTime"
                style="width: 180px"
                class="mr-4"
                :max-time="tm.endTime"
                placeholder="开始时间"
                start="00:00"
                end="24:00"
                :picker-options="{
                  start: '08:30',
                  step: '00:15',
                  end: '18:30'
                }"
                @change="checkOverlap(tm,item.timeList,index)"
              />
              <el-time-select
                v-model="tm.endTime"
                style="width: 180px"
                placeholder="结束时间"
                start="00:00"
                end="24:00"
                :min-time="tm.startTime"
                :picker-options="endTimeOptions"
                @change="checkOverlap(tm,item.timeList,index)"
              />
              <el-button type="danger" @click="removeAutoTime(item,index)" style="width: 20px;height: 20px;margin-left: 10px" :icon="Delete" circle />
              <span style="text-align: center;color: red;display: flex;align-items: center;padding-left: 10px" v-if="tm.remind">{{ tm.remind }}</span>
            </el-row>
          </div>
        </el-col>
      </el-row>
      </div>

      <el-divider content-position="center">证照信息</el-divider>

      <el-row>
        <el-col :span="6">
          <el-form-item label="头像" prop="photo">
            <UploadImg :disabled="formDisabled" v-model="formData.photo" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="查证结果" prop="verifiyImgUrl">
            <UploadImg :disabled="formDisabled" v-model="formData.verifiyImgUrl" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="职称证" prop="titleImgUrl">
            <UploadImg :disabled="formDisabled" v-model="formData.titleImgUrl" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="胸牌/执业证/资格证/职称证任选其一" prop="personalImgUrl">
            <UploadImg :disabled="formDisabled" v-model="formData.personalImgUrl" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="执业证" prop="occupationImgUrls">
            <UploadImgs :disabled="formDisabled" v-model="formData.occupationImgUrls" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="资格证" prop="qualificationImgUrls">
            <UploadImgs :disabled="formDisabled" v-model="formData.qualificationImgUrls" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
      </el-row>


<!--      <el-form-item label="海南合同" prop="hnContractImgUrls">-->
<!--        <UploadImgs :disabled="formDisabled" v-model="formData.hnContractImgUrls" height="100px" width="100px"/>-->
<!--      </el-form-item>-->

<!--      <el-form-item label="成都合同" prop="cdContractImgUrls">-->
<!--        <UploadImgs :disabled="formDisabled" v-model="formData.cdContractImgUrls" height="100px" width="100px"/>-->
<!--      </el-form-item>-->

<!--      <el-form-item label="武汉合同" prop="whContractImgUrls">-->
<!--        <UploadImgs :disabled="formDisabled" v-model="formData.whContractImgUrls" height="100px" width="100px"/>-->
<!--      </el-form-item>-->


      <el-row>
        <el-col :span="6">
          <el-form-item label="医生电子签章" prop="doctorElectronSignChapterUrl">
            <div>
            <UploadImg disabled  v-model="formData.doctorElectronSignChapterUrl" height="100px" width="100px"/>
              <el-button v-if="!formData.doctorElectronSignChapterUrl && !formDisabled" type="primary" v-loading="electronLoading" @click="upOrDelUrl">生成签章</el-button>
              <el-button v-if="formData.doctorElectronSignChapterUrl && !formDisabled" type="danger"  v-loading="electronLoading" @click="upOrDelUrl">删除签章</el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="医生电子签名" prop="doctorElectronSignImgUrl">
            <div>
              <UploadImg disabled v-model="formData.doctorElectronSignImgUrl" height="100px" width="100px"/>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="身份证正面" prop="idCardFrontImgUrl">
            <UploadImg :disabled="formDisabled" v-model="formData.idCardFrontImgUrl" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="身份证反面" prop="idCardReverseImgUrl">
            <UploadImg :disabled="formDisabled" v-model="formData.idCardReverseImgUrl" height="100px" width="100px"/>
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="center">备案信息</el-divider>

      <el-row>
        <el-col :span="8">
          <el-form-item label="民族" prop="nationCode">
            <el-select
              v-model="formData.nationCode"
              placeholder="请选择民族"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.NATION)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="学历" prop="formalLevel">
            <el-select
              v-model="formData.formalLevel"
              placeholder="请选择学历"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.FORMAL_LEVEL)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="机构省份" prop="orgProvinceCode">
            <el-select
              v-model="formData.orgProvinceCode"
              placeholder="请选择机构所在省份"
              clearable
              class="!w-240px"
            >
              <el-option
                v-for="province in provinceList"
                :key="province.id"
                :label="province.name"
                :value="province.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="通信地址" prop="address">
        <el-input :maxlength="200" :max="200" v-model="formData.address" placeholder="请输入通信地址"/>
      </el-form-item>

      <el-divider content-position="center" style="margin-top: 50px;margin-bottom: 20px">
        个人工作信息
      </el-divider>
      <div style="text-align: right;margin-right: 20px;margin-bottom: 30px">
        <el-button
          type="success"
          v-if="!formDisabled"
          @click="addWorkRecord"
          v-hasPermi="['hospital:inquiry-hospital:update']"
        >
          新增工作信息
        </el-button>
      </div>

      <el-row v-for="(item,index) in jobList" :key="item">
        <el-col :span="4" style="width: 90%;text-align: center">
          <el-form-item label="开始时间" prop="startDate">
            <el-date-picker
              v-model="item.startDate"
              type="date"
              value-format="x"
              placeholder="开始时间"
              @change="validateDates(index,0)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" style="text-align: center">
          <el-form-item label="结束时间" prop="endDate">
            <el-date-picker
              v-model="item.endDate"
              type="date"
              value-format="x"
              placeholder="结束时间"
              @change="validateDates(index,1)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="5">
          <el-form-item label="单位名称" prop="workUnitName">
            <el-input :maxlength="30" :max="30" v-model="item.workUnitName" placeholder="请输入工作单位名称"/>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="技术职务" prop="jobPosition">
            <el-input :maxlength="30" :max="30" v-model="item.jobPosition" placeholder="请输入技术职务"/>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="证明人" prop="prover">
            <el-input :maxlength="20" :max="20" v-model="item.prover" placeholder="请输入证明人"/>
          </el-form-item>
        </el-col>
        <el-col :span="3" style="text-align: center">
          <el-button
            type="danger"
            @click="delWorkRecord(index)"
            v-if="!formDisabled"
            v-hasPermi="['hospital:inquiry-hospital:update']"
          >
            删除
          </el-button>
        </el-col>
      </el-row>

      <el-divider content-position="center">个人执业信息</el-divider>

      <el-row>
        <el-col :span="12">
          <el-form-item label="执业开始时间" prop="startPracticeTime">
            <el-date-picker
              v-model="formData.startPracticeTime"
              type="date"
              value-format="x"
              @change="validateZYDates(0)"
              placeholder="选择日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="执业结束时间" prop="endPracticeTime">
            <el-date-picker
              v-model="formData.endPracticeDate"
              type="date"
              value-format="x"
              @change="validateZYDates(1)"
              placeholder="选择日期"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="执业证号" prop="professionalNo">
            <el-input :maxlength="64" :max="30" v-model="formData.professionalNo" placeholder="请输入执业证号"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="取得时间" prop="professionalTime">
            <el-date-picker
              v-model="formData.professionalTime"
              type="date"
              value-format="x"
              placeholder="选择日期"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <el-form-item label="资格证号" prop="qualificationNo">
            <el-input :maxlength="64"  v-model="formData.qualificationNo" placeholder="请输入资格证号"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="取得时间" prop="qualificationTime">
            <el-date-picker
              v-model="formData.qualificationTime"
              type="date"
              value-format="x"
              placeholder="选择日期"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <el-form-item label="职称证号" prop="titleNo">
            <el-input v-model="formData.titleNo" placeholder="请输入职称证号" maxlength="64" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="取得时间" prop="titleTime">
            <el-date-picker
              v-model="formData.titleTime"
              type="date"
              value-format="x"
              placeholder="选择日期"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="center">收款信息</el-divider>

      <el-row>
        <el-col :span="8">
          <el-form-item label="收款人姓名" prop="payeeName" style="width: 90%;text-align: center ">
            <el-input :maxlength="20" :max="20" v-model="formData.payeeName" placeholder="请输入收款人姓名"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="收款人身份证" prop="payeeIdCard"
            style="width: 90%;text-align: center">
            <el-input :maxlength="18" :max="18" v-model="formData.payeeIdCard" placeholder="请输入收款人身份证号码"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            label="收款人手机号" prop="payeeTelPhone"
            style="width: 90%;text-align: center">
            <el-input :maxlength="11" :max="11" v-model="formData.payeeTelPhone" placeholder="请输入收款人手机号"/>
          </el-form-item>
        </el-col>
      </el-row>


      <el-row>
        <el-col :span="12">
          <el-form-item label="银行卡号" prop="payeeBankNo" style="width: 90%;text-align: center">
            <el-input :maxlength="30" :max="30" v-model="formData.payeeBankNo" placeholder="请输入银行卡号"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行" prop="payeeBankName" style="width: 90%;text-align: center">
            <el-input :maxlength="128" :max="128" v-model="formData.payeeBankName" placeholder="请输入开户行"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>


    <el-divider content-position="center">审核记录</el-divider>
    <el-row v-for="item in auditedRecordList" :key="item">
      <el-col :span="6" style="text-align: center;margin-top: 10px">
        {{ item.auditorName }}
      </el-col>
      <el-col :span="6" style="text-align: center;margin-top: 10px">
        {{ getDictObj(DICT_TYPE.DOCTOR_AUDIT_RESULT, item.auditResult).label }}
      </el-col>
      <el-col :span="6" style="text-align: center;margin-top: 10px">
        {{ item.diffReason }}
      </el-col>
      <el-col :span="6" style="text-align: center;margin-top: 10px">
        {{ formatDate(item.auditTime) }}
      </el-col>
    </el-row>

    <DoctorAuditForm ref="doctorAuditFormRef" @success="auditEnd"/>

    <template #footer>
      <el-button v-if="formType == 'create' || formType == 'sync' || formType == 'update'" @click="submitForm" type="primary" :disabled="formLoading">确
        定
      </el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import {
  AuditParamVo,
  InquiryDoctorApi,
  InquiryDoctorHospitalRelationVO,
  InquiryDoctorJobVO,
  InquiryDoctorVO
} from '@/api/inquiry/doctor'
import {DICT_TYPE, getDictObj, getIntDictOptions} from "@/utils/dict";
import {deptStrictlyProps, listToTree} from "@/utils/tree";
import {AreaVO, getAreaProvince} from "@/api/system/area";
import {InquiryHospitalApi, InquiryHospitalVO} from "@/api/inquiry/hospital";
import DoctorAuditForm from "@/views/inquiry/doctor/DoctorAuditForm.vue";
import {formatDate} from "@/utils/formatTime";
import UserSelect from "@/views/system/user/UserSelect.vue";
import * as UserApi from "@/api/system/user";
import {
  DOCTOR_PROFESSION_IDENTIFICATION_TYPE,
  ID_CARD_REGEX,
  MOBILE_REGEX,
  SystemEnvTagEnum, SystemRoleCodeEnum
} from "@/utils/constants";
import {
  InquiryHospitalDepartmentRelationApi
} from "@/api/inquiry/hospital/inquiryhospitaldepartmentrelation";

import {Plus,Delete} from "@element-plus/icons-vue";
import {buildProfessionIdentificationItems, InquiryPharmacistApi} from "@/api/inquiry/pharmacist";


/** 医生信息 表单 */
defineOptions({name: 'InquiryDoctorForm'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const formTypeEnum = {
  CREATE: "create", //新增
  UPDATE: "update", //更新
  AUDIT: "audit",//审核
  DETAIL: "detail", //查看
  SYNC: "sync" //同步
}

const deptProps = {
  children: 'children',
  label: 'deptName',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: false // 用于 cascader 组件：在选中节点改变时，是否返回由该节点所在的各级菜单的值所组成的数组，若设置 false，则只返回该节点的值
}


const jobList = ref<InquiryDoctorJobVO[]>([]) //过往履历

const hospitalList = ref<InquiryHospitalVO[]>([]) // 医院列表


const auditedRecordList = ref<AuditParamVo[]>([]) // 审核信息列表

const getHospitalList = async () => {
  hospitalList.value = await InquiryHospitalApi.getInquiryHospitalList()
}

const electronLoading = ref(false)

const formRulesMobile = reactive({
  mobile: [{required: true, pattern: MOBILE_REGEX, message: '请输入正确的手机号码', trigger: 'blur'}],
})

const hospitalDoctorList = ref<InquiryDoctorHospitalRelationVO[]>([]) //

const provinceList = ref<AreaVO[]>([]) // 省份列表

const getDeptList = async (item : any) =>{
  item.deptPref = undefined
  // 获得部门列表
  let newVar = await InquiryHospitalDepartmentRelationApi.getInquiryHospitalDepartmentListByhospitalPref(item.hospitalPref);
  item.deptList =  listToTree(newVar,{id: 'deptId', children: 'children', pid: 'deptParentId'})
}

const deptChange = async (item : any) =>{
  //判断当前item的hospitalDeptRelationId   在当前hospitalDoctorList中是否存在，存在则将当前item的hospitalDeptRelationId赋清空
  if(hospitalDoctorList.value.filter((h : any) => h.hospitalDeptRelationId === item.hospitalDeptRelationId ).length > 1){
    message.alertError("当前医院、科室已存在，请勿重复添加")
    item.hospitalDeptRelationId = []
    return
  }
}

const getAreaInfo = async () => {
  provinceList.value = []
  provinceList.value = await getAreaProvince()
}


// 禁用今天之后的日期
const disabledDate = (time) => {
  const today = new Date();
  today.setHours(23, 59, 59, 999); // 可选：包含今天的最后一刻
  return time.getTime() > today;
};

const userData = ref<UserApi.UserVO>({
  id: undefined,
  nickname: undefined,
  sex: undefined,
  idCard: undefined,
  mobile: undefined,
})
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const guid = ref('')  // 旧系统医生guid 用于同步
const formData = ref({
  id: undefined,
  guid: undefined,
  name: undefined,
  sex: undefined,
  idCard: undefined,
  mobile: undefined,
  firstPracticeName: undefined,
  firstPracticeLevel: undefined,
  titleCode: undefined,
  firstPracticeDeptName: undefined,
  canal: undefined,
  inviterName: undefined,
  inviterNo: undefined,
  verifiyImgUrl: undefined,
  titleImgUrl: undefined,
  personalImgUrl: undefined,
  occupationImgUrls: [],
  qualificationImgUrls: [],
  hnContractImgUrls: [],
  cdContractImgUrls: [],
  whContractImgUrls: [],
  // userDisabled:undefined,
  doctorElectronSignChapterUrl: undefined,
  doctorElectronSignImgUrl: undefined,
  idCardFrontImgUrl: undefined,
  idCardReverseImgUrl: undefined,
  autoGrabStatus: false,
  cooperation: undefined,

  nationCode: undefined,
  address: undefined,
  formalLevel: undefined,
  orgProvinceCode: undefined,
  doctorMedicareNo: undefined,

  jobItems: [],

  hospitalDoctorItems: [],
  auditedRecords: [],

  startPracticeTime: undefined,
  endPracticeDate: undefined,
  professionalNo: undefined,
  professionalTime: undefined,
  qualificationNo: undefined,
  qualificationTime: undefined,
  titleNo: undefined,
  titleTime: undefined,

  payeeName: undefined,
  payeeIdCard: undefined,
  payeeTelPhone: undefined,
  payeeBankNo: undefined,
  payeeBankName: undefined,

  userId: undefined,
  auditStatus: undefined,
  cooperation: undefined,
  lastInquiryTime: undefined,
  startInquiryTime: undefined,
  endInquiryTime: undefined,
  envTag: SystemEnvTagEnum.PROD,
  photo: undefined,
  biography: undefined,
  professionalDec: undefined,
  jobType: undefined,
  prescriptionPasswordStatus: undefined,
  prescriptionPassword: undefined,
  disable: undefined,
})
const formRules = reactive({
  name: [{required: true, message: '医生名称不能为空', trigger: 'blur'}],
  mobile: [{required: true, message: '手机号码不能为空', trigger: 'blur'}],
  sex: [{required: true, message: '性别为必选项', trigger: 'blur'}],
  idCard: [{ required: true,pattern: ID_CARD_REGEX, message: '身份证号码格式不正确', trigger: 'blur' }],
  firstPracticeName: [{required: true, message: '第一执业机构不能为空', trigger: 'blur'}],
  firstPracticeLevel: [{required: true, message: '第一执业机构等级不能为空', trigger: 'blur'}],
  jobType: [{required: true, message: '医生类型不能为空', trigger: 'blur'}],
  envTag: [{required: true, message: '环境标识不能为空', trigger: 'blur'}],
  canal: [{required: true, message: '医生来源渠道不能为空', trigger: 'blur'}],
  autoGrabStatus: [{required: true, message: '自动抢单状态必选', trigger: 'blur'}],
  cooperation: [{required: true, message: '医生合作状态不能为空', trigger: 'blur'}],
  titleCode: [{required: true, message: '职称不能为空', trigger: 'blur'}],
  // firstPracticeDeptName: [{required: true, message: '科室不能为空', trigger: 'blur'}],
  photo: [{required: true, message: '请上传医生头像', trigger: 'blur'}],
  idCardFrontImgUrl: [{required: true, message: '请上传身份证正面照', trigger: 'blur'}],
  idCardReverseImgUrl: [{required: true, message: '请上传身份证反面照', trigger: 'blur'}],
  professionalDec: [{required: true, message: '擅长专业不能为空', trigger: 'blur'}],
  biography: [{required: true, message: '个人简介不能为空', trigger: 'blur'}],
  startPracticeTime: [{required: true, message: '开始执业时间不能为空', trigger: 'blur'}],
  professionalNo: [{required: true, message: '执业证书号不能为空', trigger: 'blur'}],
})
const formRef = ref() // 表单 Ref

const formDisabled = ref(false) //不可编辑
watch(
  () => formData.value.mobile,
  (nv) => {

    if (formType.value == formTypeEnum.AUDIT || formType.value == formTypeEnum.DETAIL) {
      formDisabled.value =  true
      return
    }
    formDisabled.value =  !MOBILE_REGEX.test(nv)
  }, {
    deep: true,
    immediate: true
  }
)

const validateDates = (index,type) => {
  const item = jobList.value[index];
  if (item.startDate && item.endDate && new Date(item.startDate) > new Date(item.endDate)) {
    // 开始时间大于结束时间，处理错误（比如显示错误消息，或者重置结束时间）
    message.alertError('结束时间不能早于开始时间')
    // 如果需要，可以重置结束时间为开始时间
    if(type == 0){
      item.startDate = undefined
    }
    if(type == 1){
      item.endDate = undefined
    }
  }
};


const validateZYDates = (type) => {
  if (new Date(formData.value.startPracticeTime) > new Date(formData.value.endPracticeDate)) {
    message.alertError('结束时间不能早于开始时间')
    // 如果需要，可以重置结束时间为开始时间
    if(type == 0){
      formData.value.startPracticeTime = undefined
    }
    if(type == 1){
      formData.value.endPracticeDate = undefined
    }
  }
};

const upOrDelUrl =async () => {
 try {
     const data={
    userId:formData.value.userId
  }
  electronLoading.value = true
  if(formData.value.doctorElectronSignChapterUrl){
    await InquiryDoctorApi.deleteElectronicSignature(data)
    formData.value.doctorElectronSignChapterUrl = ""
  }
  else{
    formData.value.doctorElectronSignChapterUrl = await InquiryDoctorApi.createElectronicSignature(data)
  }
 } catch (e) {
  console.log(e);
  
 }finally{
   electronLoading.value = false
 }
 
}

const mobleDisabled = ref(false) //详情 和 审核页面 不可编辑
watch(
  () => formType.value,
  (newFormType) => {
    mobleDisabled.value = (newFormType === formTypeEnum.AUDIT || newFormType === formTypeEnum.DETAIL)
  }
);

const autoCheckChange = async(item) =>{
  if(item.autoInquiryWayType.length <= 0){
    item.timeList = []
    item.autoInquiryTime = []
  }
}

/**
 * 删除自动开方时段按钮
 */
const removeAutoTime = async (item : InquiryDoctorHospitalRelationVO,index : number) =>{
  item.timeList.splice(index,1)
}

/**
 * 添加自动开方时段按钮
 */
const addAutoInquiryTime = async (item : InquiryDoctorHospitalRelationVO) => {
  let addParam = {startTime:undefined,endTime:undefined,remind:undefined}
  item.timeList.push(addParam)
}

// 检查重叠的函数
const checkOverlap = (tm ,timeList ,index) => {
  let currStart = '1970-01-01 '+tm.startTime
  let currEnd = '1970-01-01 '+tm.endTime
  const currentTimeRange = {
    start: new Date(currStart),
    end: new Date(currEnd),
  };

  tm.remind = ''; // 先清空当前的重叠提示

  // 遍历已选择的时间段，检查是否有重叠
  for (let i = 0; i < timeList.length; i++) {
    if (i !== index) {
      let otherStart ='1970-01-01 '+ timeList[i].startTime
      let otherEnd ='1970-01-01 '+ timeList[i].endTime
      const otherTimeRange = {
        start: new Date(otherStart),
        end: new Date(otherEnd),
      };

      // 判断两个时间段是否重叠
      if (
        (currentTimeRange.start < otherTimeRange.end && currentTimeRange.end >  otherTimeRange.start)
      ) {
        tm.remind = '时间段与已选择的时间重叠，请选择其他时间段。';
        // 如果需要，可以在这里重置或阻止时间段的更改
        // 例如：timeList.value[index].endTime = ''; // 重置结束时间
        break;
      }
    }
  }
}

/** 打开弹窗 */
const open = async (type: string, id?: number|string) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  jobList.value = []
  auditedRecordList.value = []
  hospitalDoctorList.value = []
  // 修改时，设置数据
  if(type == 'sync'){
    await queryInquiryDoctorSync(id)
    guid.value = id;
  }else if (id) {
    await getDoctorDetailById(id)
  }

  // 获取省区列表
  await getAreaInfo()
  // 加载医院列表
  await getHospitalList()
}

const getDoctorDetailById = async (id) => {
  formLoading.value = true
  try {
    const response = await InquiryDoctorApi.getInquiryDoctor(id)
    if (response && response.orgProvinceCode &&  typeof response.orgProvinceCode === 'string') {
      response.orgProvinceCode = Number(response.orgProvinceCode);
    }
    formData.value = response
    jobList.value = typeof formData.value.jobItems == null ? [] : formData.value.jobItems
    hospitalDoctorList.value = typeof formData.value.hospitalDoctorItems == null ? [] : formData.value.hospitalDoctorItems
    formData.value.autoGrabStatus = response.autoGrabStatus != 0
    hospitalDoctorList.value.forEach(im =>{
      if(im.autoInquiryTime != undefined){
        im.timeList = []
        im.autoInquiryTime.forEach(tm =>{
          let time = {startTime:tm.split('|')[0],endTime:tm.split('|')[1],remind:undefined}
          if(time.startTime != undefined && time.endTime != undefined){
            im.timeList.push(time)
          }
        })
      }
    })
    auditedRecordList.value = typeof formData.value.auditedRecords == null ? [] : formData.value.auditedRecords
    if(formType.value === formTypeEnum.UPDATE){
      // formData.value.userDisabled = true
      mobleDisabled.value = true
    }
  } finally {
    formLoading.value = false
  }
}

/**查询旧系统同步医生数据*/
const queryInquiryDoctorSync = async (guid:any) =>{
  formLoading.value = true
  try {
    const response = await InquiryDoctorApi.queryInquiryDoctorSync(guid)
    formData.value = response
    formData.value.autoGrabStatus = response.autoGrabStatus != 0
    if(formType.value === formTypeEnum.SYNC){
      // formData.value.userDisabled = true
      mobleDisabled.value = true
    }
  } finally {
    formLoading.value = false
  }
}

defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  if (formType.value === formTypeEnum.AUDIT) {
    dialogVisible.value = false
    return
  }
  formData.value.jobItems = jobList;
  let msg = ''
  hospitalDoctorList.value.forEach(hosd =>{
    if(hosd.hospitalPref == undefined || hosd.hospitalDeptRelationId == undefined){
      msg = '关联医院或医院对应科室未选'
    }
    if(hosd.inquiryWayType.length == 0 && hosd.autoInquiryWayType.length == 0){
      msg = '关联医院时，需最少选择一种接诊方式'
    }
    hosd.autoInquiryTime = []
    hosd.timeList.forEach(tm =>{
      if(tm.startTime == undefined || tm.endTime ==  undefined){
        msg = '自动开方出诊时段中开始时间和结束时间必选'
      }
      if(tm.remind != undefined && tm.remind.length > 0){
        msg = '自动开方出诊时段重复，请修改'
      }
      hosd.autoInquiryTime.push(tm.startTime+"|"+tm.endTime)
    })
  })
  if(msg != ''){
    await message.alertWarning(msg);
    return
  }
  formData.value.hospitalDoctorItems = hospitalDoctorList;
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    formData.value.autoGrabStatus = formData.value.autoGrabStatus ? 1 : 0
    const data = formData.value as unknown as InquiryDoctorVO
    if (formType.value === 'create' && !formData.value.id) {
      await InquiryDoctorApi.createInquiryDoctor(data)
      dialogVisible.value = false

    } else if(formType.value === 'sync'){
      data.guid = guid.value
      let pha = await InquiryDoctorApi.getInquiryDoctorByMobile(data.mobile);
      if(pha?.id){
        // 二次确认
        ElMessageBox.confirm('当前医生信息已存在,是否确认同步覆盖医生信息(包括但不限于基础信息、CA、签名等)', '提示',{
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          await InquiryDoctorApi.syncCreateInquiryDoctor(data)
          dialogVisible.value = false
        }).catch(() => {
        });
      }else{
        await InquiryDoctorApi.syncCreateInquiryDoctor(data)
        dialogVisible.value = false
      }

    }else{
      await InquiryDoctorApi.updateInquiryDoctor(data)
      dialogVisible.value = false
    }

    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const userInfo = async (param:UserApi.UserVO) =>{
  // if(!param.onlyDefaultTenant){
  //   let mobile = formData.value.mobile;
  //   message.alertError('当前手机号【'+mobile+'】已被门店注册，请解绑门店/注销账号后注册医生账号!')
  //   formData.value.mobile = undefined
  //   return
  // }
  formLoading.value = true;
  userData.value = param ?? {}
  let mobile = formData.value.mobile;
  resetForm()
  formData.value.userId = param?.id
  formData.value.name = param?.nickname
  formData.value.sex = param?.sex
  formData.value.idCard = param?.idCard
  if(param?.mobile){
    formData.value.mobile =param?.mobile
    let pha = await InquiryDoctorApi.getInquiryDoctorByMobile(param.mobile);
    if(pha?.id) {
      await getDoctorDetailById(pha.id)
    }
  }else{
    formData.value.mobile = mobile
  }
  formLoading.value = false;

}

/**
 * 添加工作履历
 */
const addWorkRecord = async () => {
  let item = {}
  jobList.value.push(item)
}

const addHospitalRel = async () => {
  let item = {
    hospitalPref: undefined,
    doctorPref: undefined,
    inquiryWayType: [],
    autoInquiryWayType: [],
    autoInquiryTime: [],
    timeList: []
  }
  hospitalDoctorList.value.push(item)
}

const delWorkRecord = async (index: number) => {
  jobList.value.splice(index, 1)
}

const delHospitalRel = async (index: number) => {
  hospitalDoctorList.value.splice(index, 1)
}

const doctorAuditFormRef = ref() // 表单 Ref
const openAuditForm = (guid: string) => {
  doctorAuditFormRef.value.open(guid)
}

const auditEnd = async () => {
  dialogVisible.value = false
  // 发送操作成功的事件
  emit('success')
}


/** 重置表单 */
const resetForm = () => {
  formData.value = {
    auditedRecordItems: [],
    id: undefined,
    guid: undefined,
    name: undefined,
    sex: undefined,
    idCard: undefined,
    mobile: undefined,
    firstPracticeName: undefined,
    firstPracticeLevel: undefined,
    titleCode: undefined,
    firstPracticeDeptName: undefined,
    autoGrabStatus: false,
    cooperation: undefined,
    canal: undefined,
    inviterName: undefined,
    inviterNo: undefined,
    // userDisabled:undefined,
    photo: undefined,
    verifiyImgUrl: undefined,
    titleImgUrl: undefined,
    personalImgUrl: undefined,
    occupationImgUrls: [],
    qualificationImgUrls: [],
    hnContractImgUrls: [],
    cdContractImgUrls: [],
    whContractImgUrls: [],
    doctorElectronSignChapterUrl: undefined,
    doctorElectronSignImgUrl: undefined,
    idCardFrontImgUrl: undefined,
    idCardReverseImgUrl: undefined,
    nationCode: undefined,
    address: undefined,
    formalLevel: undefined,
    orgProvinceCode: undefined,
    jobItems: [],
    hospitalDoctorItems: [],
    startPracticeTime: undefined,
    endPracticeDate: undefined,
    professionalNo: undefined,
    professionalTime: undefined,
    qualificationNo: undefined,
    qualificationTime: undefined,
    titleNo: undefined,
    titleTime: undefined,
    payeeName: undefined,
    payeeIdCard: undefined,
    payeeTelPhone: undefined,
    payeeBankNo: undefined,
    payeeBankName: undefined,
    userId: undefined,
    auditStatus: undefined,
    cooperation: undefined,
    lastInquiryTime: undefined,
    startInquiryTime: undefined,
    endInquiryTime: undefined,
    envTag: SystemEnvTagEnum.PROD,
    biography: undefined,
    professionalDec: undefined,
    jobType: undefined,
    doctorMedicareNo: undefined,
    prescriptionPasswordStatus: undefined,
    prescriptionPassword: undefined,
    disable: undefined
  }
  formRef.value?.resetFields()
  hospitalDoctorList.value = []
  jobList.value = []//过往履历
  auditedRecordList.value = [] // 审核信息列表
}
</script>
<style>
.el-divider__text {
  font-weight: 700;
}

.el-divider--horizontal {
  margin: 50px 0;
}

.el-textarea__inner {
  height: 100%;
}
</style>
