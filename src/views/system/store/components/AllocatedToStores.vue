<template>
  <Dialog title="分配门店" v-model="dialogVisible" width="70%">
    <!-- 操作按钮 -->
    <ContentWrap>
      <el-row :gutter="10" class="mb-20px">
        <el-col :span="1.5">
          <el-button type="primary" @click="openAddStoreDialog" :icon="Plus" plain>添加门店</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
type="danger" :disabled="selectedAssignedStores.length === 0" @click="handleRemoveSelectedStores"
            :icon="Delete" plain>
            删除门店
          </el-button>
        </el-col>
      </el-row>
    </ContentWrap>

    <!-- 已分配门店列表 -->
    <ContentWrap>
      <el-table
v-loading="assignedLoading" :data="assignedStoreList" :stripe="true"
        @selection-change="handleAssignedSelectionChange">
        <el-table-column type="selection" width="40" />
        <el-table-column label="编码" align="center" prop="tenantPref" min-width="70" />
        <el-table-column label="门店名" align="center" prop="tenantName" min-width="60" />
        <el-table-column label="联系人" align="center" prop="contactName" />
        <el-table-column label="联系手机" align="center" prop="contactMobile" min-width="70" />
        <el-table-column label="地区" align="center" min-width="70">
          <template #default="scope">
            {{ scope.row.province }} / {{ scope.row.city }} / {{ scope.row.area }}
          </template>
        </el-table-column>
        <el-table-column label="地址" align="center" prop="address" min-width="70" />
      </el-table>

      <!-- 自定义分页组件 -->
      <Pagination
v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" :total="total"
        @pagination="handlePaginationChange" class="mt-20px" />
    </ContentWrap>
    <template #footer>
      <el-button @click="dialogVisible = false">{{ t('common.close') }}</el-button>
    </template>

    <!-- 添加门店的对话框 -->
    <TenantTable ref="addStoresRef"  @success="handleAddSelectedStoresFromComponent" />
  </Dialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import AddStores from '@/views/system/store/components/AllocatedToStores/AddStores.vue'
import { TenantPackageShareRelationApi } from '@/api/system/tenantPackageShareRelation'
import { getTenantId } from "@/utils/auth";
import TenantTable from "@/views/system/tenant/TenantTable.vue"
interface StoreItem {
  id: number
  pref: string
  name: string
  contactName: string
  contactMobile: string
  province: string
  city: string
  area: string
  address: string
}

interface QueryParams {
  pageNo: number
  pageSize: number
  headTenantId?: number // 总部ID
  tenantId?: number     // 门店ID
  bizType?: number      // 业务类型
  tenantPackageId?: number // 套餐ID
  ext?: string          // 扩展字段
  createTime?: string[] // 时间范围
}

const { t } = useI18n()
const packageId = ref<number | undefined>(undefined)
const dialogVisible = ref(false)
const assignedStoreList = ref<StoreItem[]>([])
const assignedLoading = ref(false)
const selectedAssignedStores = ref<StoreItem[]>([])
const addStoresRef = ref()
const total = ref(0)
const headTenantId = getTenantId()
const queryParams = reactive<QueryParams>({
  pageNo: 1,
  pageSize: 10,
  headTenantId: undefined,
  tenantPackageId: 572,
  createTime: undefined
})

// 处理分页变化
const handlePaginationChange = (pagination: { page: number; limit: number }) => {
  queryParams.pageNo = pagination.page
  queryParams.pageSize = pagination.limit
  loadAssignedStores()
}

// 加载已分配门店
const loadAssignedStores = async () => {
  if (!packageId.value) return
  assignedLoading.value = true
  try {
    const res = await TenantPackageShareRelationApi.tenantPackageShareRelationPage(queryParams)

    if (res.code === 0) {
      const storeList = res.data.list || []
      assignedStoreList.value = storeList
      total.value = res.data.total
    } else {
      ElMessage.error(res.msg || '获取门店列表失败')
    }
  } catch (error) {
    console.error('获取门店列表失败:', error)
    ElMessage.error('获取门店列表失败')
  } finally {
    assignedLoading.value = false
  }
}

// 打开添加门店弹窗
const openAddStoreDialog = () => {
  addStoresRef.value.open(headTenantId,'headTenant')
}

// 移除选中的门店
const handleRemoveSelectedStores = async () => {
  debugger
  if (selectedAssignedStores.value.length === 0) return

  try {
    await ElMessageBox.confirm(
      '确认要删除选中的门店吗？',
      t('common.confirmTitle'),
      {
        confirmButtonText: t('common.ok'),
        cancelButtonText: t('common.cancel'),
        type: 'warning',
      }
    )

    // 调用API删除
    const deteleList = selectedAssignedStores.value.map(store => {return store.id})
    await TenantPackageShareRelationApi.deleteTenantPackageShareRelation({
      ids:deteleList
    })
    ElMessage.success(`成功删除门店`)
    // const deletePromises = selectedAssignedStores.value.map(store =>
    //   TenantPackageShareRelationApi.deleteTenantPackageShareRelation(store.id)
    // )

    // const results = await Promise.all(deletePromises)
    // const successCount = results.filter(res => res.code === 0).length

    // if (successCount === selectedAssignedStores.value.length) {
    //   ElMessage.success(`成功删除 ${successCount} 家门店`)
    //   // 重新加载数据
    //   loadAssignedStores()
    // } else {
    //   ElMessage.warning(`成功删除 ${successCount} 家，失败 ${selectedAssignedStores.value.length - successCount} 家`)
    // }
    selectedAssignedStores.value = []
    loadAssignedStores()
  } catch (error) {
    console.log('取消删除')
  }
}

// 分配门店变化监听
const handleAssignedSelectionChange = (selection: StoreItem[]) => {
  selectedAssignedStores.value = selection
}

// 从子组件添加门店
const handleAddSelectedStoresFromComponent = async (stores: StoreItem[]) => {
  try {
    await TenantPackageShareRelationApi.createTenantPackageShareRelation({
        tenantIds: stores.map(store => {return store.id}), // 需要替换为实际的总部ID
        tenantPackageId: packageId.value,
      })
      ElMessage.success(`门店添加成功`)
    // const createPromises = TenantPackageShareRelationApi.createTenantPackageShareRelation({
    //     tenantIds: stores.map(store => {return store.id}), // 需要替换为实际的总部ID
    //     tenantPackageId: packageId.value,
    //     bizType: 1 // 根据业务需要设置
    //   })

    // const results = await Promise.all(createPromises)
    // const successCount = results.filter(res => res.code === 0).length

    // if (successCount === stores.length) {
    //   ElMessage.success(`成功添加 ${successCount} 家门店`)
    //   // 重新加载数据
    //   loadAssignedStores()
    // } else {
    //   ElMessage.warning(`成功添加 ${successCount} 家，失败 ${stores.length - successCount} 家`)
    // }
    loadAssignedStores()
  } catch (error) {
    console.error('添加门店失败:', error)
  }
}

const open = (val) => {
  packageId.value =  val.id
  queryParams.tenantPackageId = val.id
  dialogVisible.value = true
  loadAssignedStores()
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.el-card {
  margin-bottom: 20px;
}

.mb-20px {
  margin-bottom: 20px;
}
</style>