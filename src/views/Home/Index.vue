<template>
  <component :is="currentComponent" />
</template>

<script setup>
import { computed } from 'vue'
import PortalMerchantHome from '@/views/inquiry/portalMerchant/home/<USER>' // 商家首页
// import PortalMerchantHome from '@/views/inquiry/portalMerchant/home/<USER>' // 商家首页
import PortalDoctorHome from '@/views/inquiry/portalDoctor/home/<USER>' // 医生首页
import PortalPharmacistHome from '@/views/inquiry/portalPharmacist/home/<USER>' // 药师首页

import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache()

const user = wsCache.get(CACHE_KEY.USER)

// 根据登录角色返回组件
const currentComponent = computed(() => {
  if (user.drugStore && !user.pharmacist) {
    // 药店 && !药师
    return PortalMerchantHome
  } else if (user.pharmacist) {
    // 药师
    // return PortalPharmacistHome
    return PortalMerchantHome
  } else if (user.physician) {
    // 医生
    return PortalDoctorHome
  } else {
    return PortalMerchantHome
  }
})
</script>
